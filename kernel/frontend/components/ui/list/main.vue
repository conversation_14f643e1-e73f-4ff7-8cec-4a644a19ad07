<template>
    <div
        class="ui-list"
        :class="{
            'is-searchable': enableSearch,
            'compact-search': enableCompactSearch,
            'has-apply-clear': enableApplyClear
        }"
        v-if="isInitialized"
    >
        <div class="list-search" v-if="enableSearch">
            <el-input
                v-model="searchQuery"
                :placeholder="$t('Search..')"
                prefix-icon="el-icon-search"
                autocorrect="off"
                autocapitalize="off"
                spellcheck="false"
                clearable
                size="medium"
                @input="handleSearchDebounced"
            />

            <div class="list-actions" v-if="enableApplyClear && enableSearch">
                <el-button type="success" icon="far fa-check" @click="$emit('applied')" />
                <el-button type="danger" icon="far fa-broom" @click="$emit('cleared')" />
            </div>
        </div>

        <ui-table
            ref="table"
            :collection="collection"
            :get-rows="getRows"
            :items="items"
            :columns="columns"
            :search="search"
            :search-text="searchText"
            :filters="listFilters"
            :extra-fields="extraFields"
            :single-select="singleSelect"
            :select-again="selectAgain"
            :options="tableOptions"
            :disable-no-rows-overlay="disableNoRowsOverlay"
            :enable-row-handle="false"
            :enable-text-search="!!enableTextSearch"
            :force-full-text-search="!!forceFullTextSearch"
            :row-height="itemHeight"
            no-zebra
            @selected="handleSelect"
            @double-clicked="$emit('double-clicked', $event)"
        />
    </div>
</template>

<script>
import _ from 'lodash';
import {template} from 'framework/helpers';

export default {
    props: {
        collection: String,
        getRows: Function,
        items: {
            type: [Array, Object],
            default() {
                return null;
            }
        },
        filters: {
            type: Object,
            default: () => ({})
        },
        valueFrom: {
            type: String,
            default: '_id'
        },
        labelFrom: {
            type: String,
            default: 'name'
        },
        countFrom: String,
        enableSelect: Boolean,
        singleSelect: {
            type: Boolean,
            default: true
        },
        multiSelectWithClick: Boolean,
        selectAgain: Boolean, // Emit selected even if row is already selected.
        extraFields: {
            type: Array,
            default: () => []
        },
        selectedItems: {
            type: Array,
            default: () => []
        },
        value: {
            type: Array,
            default: () => []
        },
        enableSearch: Boolean,
        enableCompactSearch: {
            type: Boolean,
            default: true
        },
        enableApplyClear: {
            type: Boolean,
            default: false
        },
        enableTextSearch: Boolean,
        forceFullTextSearch: Boolean,
        template: [String, Object],
        htmlTemplate: Function,
        itemHeight: {
            type: Number,
            default: 24
        },
        disableNoRowsOverlay: Boolean,
        dontChangeSelectionOnUpdate: Boolean
    },

    data: () => ({
        searchQuery: '',
        search: '',
        searchText: '',
        columns: [],
        tableOptions: {
            suppressContextMenu: true
        },
        isInitialized: false
    }),

    computed: {
        listFilters() {
            let filters = {};

            if (_.isObject(this.filters)) {
                filters = this.filters;
            }

            if (_.isUndefined(filters.$sort)) {
                filters.$sort = {[this.labelFrom]: 1};
            }

            return filters;
        }
    },

    watch: {
        value(value, oldValue) {
            if (!_.isEqual(value, oldValue || [])) {
                this.updateSelected();
            }
        },
        selectedItems(value, oldValue) {
            if (!_.isEqual(value, oldValue || [])) {
                this.updateSelected();
            }
        }
    },

    methods: {
        refresh() {
            this.$refs.table.refreshData();
        },
        select(id) {
            const api = this.$refs.table.api;

            api.forEachNode(node => {
                if (_.isObject(node.data) && !node.selected && _.get(node.data, this.valueFrom) === id) {
                    node.setSelected(true);
                }
            });
        },

        handleSearch(query) {
            query = query.trim();

            if (query.length < 3 && query.length !== 0) {
                return;
            }

            if (!!this.enableTextSearch) {
                this.searchText = query.trim();
            } else {
                this.search = query.trim();
            }
        },
        handleSelect(items) {
            const selected = items.map(item => {
                return _.get(item, this.valueFrom);
            });

            this.$emit('selected-items', items);
            this.$emit('selected', selected);
            this.$emit('input', selected);
        },
        updateSelected(rowNode) {
            const select = node => {
                if (_.isObject(node.data) && !node.selected) {
                    if (Array.isArray(this.selectedItems) && this.selectedItems.length > 0) {
                        this.selectedItems.forEach(value => {
                            if (_.get(node.data, this.valueFrom) === value) {
                                node.setSelected(true);
                            }
                        });
                    } else if (Array.isArray(this.value) && this.value.length > 0) {
                        this.value.forEach(v => {
                            if (_.get(node.data, this.valueFrom) === v) {
                                node.setSelected(true);
                            }
                        });
                    }
                }
            };

            if (rowNode) {
                select(rowNode);
            } else {
                const api = this.$refs.table.api;

                api.forEachNode(select);
            }
        },
        initialize() {
            // Initialize columns.
            let columns = [];
            let column = {};
            column.field =
                _.isString(this.template) || _.isFunction(this.htmlTemplate)
                    ? this.labelFrom + '-template'
                    : this.labelFrom;
            column.label = this.labelFrom;
            // column.checkboxSelection = !!this.enableSelect && !this.singleSelect;
            if (_.isString(this.template) && this.template) {
                column.valueFormatter = params => {
                    if (_.isObject(params.node.data)) {
                        return template(this.template, params.node.data);
                    }

                    return '';
                };
            }
            if (_.isFunction(this.htmlTemplate)) {
                column.render = params => {
                    if (_.isObject(params.node.data)) {
                        return this.htmlTemplate(params.node.data, params);
                    }

                    return '';
                };
                column.cellStyle = params => ({padding: 0});
            }
            column.searchable = !(_.isString(this.template) || _.isFunction(this.htmlTemplate));
            columns.push(column);
            if (_.isString(this.template) || _.isFunction(this.htmlTemplate)) {
                columns.push({
                    field: this.labelFrom,
                    label: this.labelFrom,
                    hidden: true
                });
            }
            if (Array.isArray(this.extraFields)) {
                _.uniq(this.extraFields.concat([this.valueFrom, this.labelFrom])).forEach(field => {
                    columns.push({
                        field: field,
                        label: field,
                        hidden: true
                    });
                });
            }
            this.columns = columns;

            // Initialize options.
            let options = {};
            options.suppressContextMenu = true;
            options.headerHeight = 0;
            // if (!this.singleSelect) {
            //     options.suppressRowClickSelection = true;
            // }
            if (!!this.multiSelectWithClick) {
                options.rowSelection = 'multiple';
                options.suppressRowDeselection = false;
                options.rowMultiSelectWithClick = true;
            }

            if (_.isString(this.collection) && (_.isNull(this.items) || this.items.length < 1)) {
                options.isRowSelectable = node => {
                    this.updateSelected(node);

                    return true;
                };
            } else {
                if (!this.dontChangeSelectionOnUpdate) {
                    options.onModelUpdated = () => {
                        this.updateSelected();
                    };
                }
            }

            options.getRowNodeId = data => _.get(data, this.valueFrom);

            this.tableOptions = options;
        }
    },

    created() {
        this.handleSearchDebounced = _.debounce(this.handleSearch, 350, {leading: false, trailing: true});

        this.initialize();

        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.ui-list.is-searchable.has-apply-clear {
    .list-search {
        display: flex;
        flex-flow: row nowrap;

        .el-input {
            flex: 1 1 0;
        }

        .list-actions {
            display: flex;
            flex-flow: row nowrap;
            padding: 5px;
        }
    }
}
</style>
