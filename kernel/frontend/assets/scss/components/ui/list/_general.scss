.ui-list {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &.is-searchable {
        padding-top: 52px;
    }

    .list-search {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 52px;
        padding: 7px 10px 9px;
        border-bottom: 1px solid $border-color;
        z-index: 1;
    }

    &.compact-search {
        &.is-searchable {
            padding-top: 36px;
        }

        .list-search {
            height: 36px;
            padding: 0;

            .el-input__inner {
                border-radius: 0;
                border: none;
                border-bottom: 1px solid $border-color;
            }

            .el-input__inner,
            .el-textarea__inner {
                transition: none !important;

                &:focus {
                    box-shadow: none !important;
                }
            }
        }
    }

    .ag-theme-balham {
        .ag-header-row {
            border-top: none;
        }

        .ag-header {
            border-bottom: none !important;
        }

        .ag-row {
            background-color: #fff;
        }

        .ag-row-hover {
            background-color: $primary-lighter;
        }

        .ag-row-selected {
            background-color: $warning-light;
            border-bottom-color: mix(#fff, $warning, 50%);

            &:hover {
                background-color: mix(#fff, $warning, 70%);
            }
        }

        .ag-pinned-left-header,
        .ag-header-viewport,
        .ag-pinned-right-header,
        .ag-header, .ag-header-row {
            display: none !important;
        }
    }
}
