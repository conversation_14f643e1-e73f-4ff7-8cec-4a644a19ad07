const path = require('path');
const fs = require('fs');
const _ = require('lodash');
const shell = require('shelljs');
const archiver = require('archiver');
const del = require('del');
const webpack = require('webpack');
const settings = require('../settings');
const {clean, log, timeDiff} = require('../utils');
const ssh = new (require('node-ssh').NodeSSH)();

module.exports = {
    name: 'deploy',
    description: 'Deploy to remote server.',
    arguments: '[withDB]',
    async action(withDB) {
        const tempAppPath = path.join(settings.paths.tempPath, 'app');
        const configJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'config.json')));
        const pkgJson = JSON.parse(fs.readFileSync(path.join(settings.paths.rootPath, 'package.json')));
        const sshConfig = configJson.ssh;
        const deploymentConfig = configJson.deployment;
        let result = null;

        // Build
        process.env.NODE_ENV = 'production';
        process.env.IS_PACKAGED = 'yes';

        log('info', 'Please wait! Building for packaging..');

        // Clean build results.
        clean(true, true);

        await new Promise((resolve, reject) => {
            const startTime = new Date();
            webpack(
                [
                    require('../compiler/config/backend'),
                    require('../compiler/config/frontend'),
                    require('../compiler/config/vendors')
                ],
                (error, stats) => {
                    if (stats.hasErrors()) {
                        const jsonStats = stats.toJson();

                        console.log(jsonStats);

                        return;
                    }

                    if (error) {
                        log('error', error.message);

                        reject();

                        return;
                    }

                    const endTime = new Date();

                    log('success', `Build process is completed in ${timeDiff(startTime, endTime)}`);

                    if (withDB) {
                        log('info', 'Please wait! Exporting database...');
                        shell.exec(
                            `mongodump --quiet --host localhost:27017 -d "${
                                configJson.database.mongodb.db
                            }" --gzip --archive=${path.join(settings.paths.tempPath, 'entererp.db')}`
                        );
                        log('success', `Database exported successfully!`);
                    }

                    log('info', 'Please wait! Packaging...');

                    shell.exec(`mkdir ${path.join(tempAppPath)}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'bin')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'backend')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'daemon')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'frontend')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'static')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'cache')}`);
                    shell.exec(`mkdir ${path.join(tempAppPath, 'storage', 'temp')}`);

                    shell.cp('-R', path.join(settings.paths.rootPath, '/bin/linux/*'), path.join(tempAppPath, 'bin/'));
                    shell.cp(
                        '-R',
                        path.join(settings.paths.backendBuildPath, '/*'),
                        path.join(tempAppPath, 'backend/')
                    );
                    shell.cp('-R', path.join(__dirname, 'templates/daemon/*'), path.join(tempAppPath, 'daemon/'));
                    shell.cp(
                        '-R',
                        path.join(settings.paths.frontendBuildPath, '/*'),
                        path.join(tempAppPath, 'frontend/')
                    );
                    shell.cp('-R', path.join(settings.paths.staticPath, '/*'), path.join(tempAppPath, 'static/'));
                    shell.cp('-R', path.join(settings.paths.rootPath, '/yarn.lock'), path.join(tempAppPath, '/'));

                    if (withDB) {
                        shell.cp(
                            '-R',
                            path.join(settings.paths.tempPath, 'entererp.db'),
                            path.join(tempAppPath, 'entererp.db')
                        );
                    }

                    configJson.app.lastUpdatedAt = new Date();
                    configJson.app.packaged = true;
                    configJson.server.protocol = deploymentConfig.protocol;
                    configJson.server.host = deploymentConfig.host;
                    configJson.server.port = deploymentConfig.port;
                    delete configJson.deployment;
                    fs.writeFileSync(path.join(tempAppPath, 'config.json'), JSON.stringify(configJson, null, 4));

                    _.each(pkgJson.dependencies, (version, packageName) => {
                        if (
                            settings.bundledFrontendPackages.concat(settings.frontEndPackages).indexOf(packageName) !==
                            -1
                        ) {
                            delete pkgJson.dependencies[packageName];
                        }
                    });
                    delete pkgJson.devDependencies;
                    fs.writeFileSync(path.join(tempAppPath, 'package.json'), JSON.stringify(pkgJson, null, 4));

                    const output = fs.createWriteStream(path.join(settings.paths.tempPath, 'entererp.zip'));
                    const archive = archiver('zip', {
                        zlib: {level: 9} // Sets the compression level.
                    });
                    output.on('close', function () {
                        log('success', `Application is packaged successfully!`);

                        resolve();
                    });
                    archive.pipe(output);
                    archive.directory(tempAppPath, false);
                    archive.finalize();
                }
            );
        });

        // Connect to server.
        log('info', 'Please wait! Connecting to remote server..');
        try {
            if (!!process.env.SSH_PASSWORD) {
                await ssh.connect({
                    host: sshConfig.host,
                    port: sshConfig.port || '22',
                    username: sshConfig.username,
                    passphrase: process.env.SSH_PASSWORD,
                    privateKeyPath: sshConfig.privateKey
                });
            } else {
                await ssh.connect({
                    host: sshConfig.host,
                    port: sshConfig.port || '22',
                    username: sshConfig.username,
                    privateKeyPath: sshConfig.privateKey
                });
            }
        } catch (e) {
            log('error', `Could not connect to the server! Reason: ${e.message}`);

            process.exit(1);
        }
        log('success', 'Successfully connected to the remote server.');

        // Stop pm2.
        try {
            await ssh.execCommand('pm2 stop entererp');
        } catch {}

        // Remove existing remote app folder.
        await ssh.execCommand('rm -rf /opt/entererp');
        await ssh.execCommand('mkdir /opt/entererp');

        // Initialize nginx.
        log('info', 'Please wait! Initializing nginx...');
        let nginxTemplate = template(fs.readFileSync(path.join(__dirname, 'templates/nginx/entererp')), {
            host: configJson.server.host,
            port: configJson.server.port
        });
        fs.writeFileSync(path.join(tempAppPath, 'nginx.conf'), nginxTemplate);
        await ssh.execCommand('rm -rf /etc/nginx/sites-available/entererp');
        await ssh.execCommand('rm -rf /etc/nginx/sites-enabled/entererp');
        try {
            await ssh.putFile(path.join(tempAppPath, 'nginx.conf'), '/etc/nginx/sites-available/entererp');
        } catch (e) {
            log('error', `Could not put file to remote destination! Reason: ${e.message}`);

            process.exit(1);
        }
        await ssh.execCommand('ln -s /etc/nginx/sites-available/entererp /etc/nginx/sites-enabled/');
        log('success', 'Successfully initialized nginx.');

        // Put file to remote folder.
        log('info', 'Please wait! Putting the files to the remote server..');
        try {
            await ssh.putFile(path.join(settings.paths.tempPath, 'entererp.zip'), '/opt/entererp.zip');
        } catch (e) {
            log('error', `Could not put file to remote destination! Reason: ${e.message}`);

            process.exit(1);
        }
        log('success', 'Successfully put the files to the remote server.');

        // Extract
        log('info', 'Please wait! Extracting the files..');
        result = await ssh.execCommand('unzip /opt/entererp.zip -d /opt/entererp');
        if (result.code > 0) {
            console.log(result.message);
            exitWithError();
        }
        await ssh.execCommand('rm /opt/entererp.zip');
        log('success', 'Successfully extracted the files.');

        // Install dependencies.
        log('info', 'Please wait! Installing the dependencies..');
        result = await ssh.execCommand('yarn install --production --ignore-engines', {
            cwd: '/opt/entererp'
        });
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed the dependencies.');

        // Install daemon dependencies.
        log('info', 'Please wait! Installing the daemon dependencies..');
        result = await ssh.execCommand('yarn install --production --ignore-engines', {
            cwd: '/opt/entererp/daemon'
        });
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed the daemon dependencies.');

        // Restart services.
        log('info', 'Please wait! Restarting services..');
        await ssh.execCommand('pm2 flush');
        await ssh.execCommand('systemctl restart mongod');
        await ssh.execCommand('systemctl restart redis');
        await ssh.execCommand('systemctl restart nginx');
        await ssh.execCommand('redis-cli FLUSHALL');
        log('success', 'Successfully restarted the services.');

        // Import database
        if (withDB) {
            log('info', 'Please wait! Importing database..');
            result = await ssh.execCommand(
                `mongorestore --host localhost --port 27017 -d ${configJson.database.mongodb.db} --archive=/opt/entererp/entererp.db --gzip --drop --maintainInsertionOrder --stopOnError`
            );
            if (result.code > 0) exitWithError();
            log('success', `Database imported successfully!`);
        }

        log('info', 'Please wait! Starting the daemon..');
        result = await ssh.execCommand('pm2 describe entererp-daemon');
        if (result.code === 0) {
            await ssh.execCommand('pm2 restart entererp-daemon');
            await ssh.execCommand('pm2 save');
        } else {
            result = await ssh.execCommand(
                'pm2 start /opt/entererp/daemon/main.js -n entererp-daemon --cwd /opt/entererp/daemon'
            );
            await ssh.execCommand('pm2 save');
        }
        log('success', `Successfully started the daemon`);

        log('info', 'Please wait! Starting the app..');
        result = await ssh.execCommand('pm2 describe entererp');
        if (result.code === 0) {
            await ssh.execCommand('pm2 restart entererp');
            await ssh.execCommand('pm2 save');
        } else {
            result = await ssh.execCommand(
                'pm2 start /opt/entererp/backend/app.js -i max -n entererp --cwd /opt/entererp'
            );
            await ssh.execCommand('pm2 save');
        }
        log('success', `Successfully started the application. App is running at https://${deploymentConfig.host}`);

        // Install certificates.
        log('info', 'Please wait! Installing the certificates..');
        result = await ssh.execCommand(
            `certbot --nginx -d ${configJson.server.host} -n --agree-tos --redirect -m <EMAIL>`
        );
        if (result.code > 0) exitWithError();
        log('success', 'Successfully installed the certificates.');

        del.sync([
            path.join(settings.paths.tempPath, '**'),
            '!' + settings.paths.tempPath,
            '!' + path.join(settings.paths.tempPath, '.gitignore')
        ]);

        process.exit(0);
    }
};

function exitWithError() {
    log('error', 'Setup failed!');

    process.exit(1);
}

function template(str, data) {
    // Use custom template delimiters.
    _.templateSettings.escape = /{{([\s\S]+?)}}/g;
    _.templateSettings.interpolate = /{_([\s\S]+?)}}/g;
    _.templateSettings.evaluate = /{#([\s\S]+?)}}/g;

    let compiled = _.template(str);

    return compiled(data);
}
