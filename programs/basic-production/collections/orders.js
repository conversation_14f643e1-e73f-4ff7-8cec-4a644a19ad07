import _ from 'lodash';

export default {
    name: 'orders',
    title: 'Orders',
    branch: true,
    assignable: true,
    view: 'basic-production.productions.orders',
    labelParams: {
        from: 'code'
    },
    schema: {
        // General
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'approved', 'canceled'],
            default: 'draft',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        type: {
            type: 'string',
            label: 'Type',
            default: 'standard',
            index: true
        },
        stageId: {
            type: 'string',
            label: 'Stage',
            required: false,
            index: true
        },
        progress: {
            type: 'decimal',
            label: 'Progress',
            default: 0,
            index: true
        },
        bomId: {
            type: 'string',
            label: 'Product',
            index: true
        },
        unitId: {
            type: 'string',
            label: 'Unit',
            index: true
        },
        quantity: {
            type: 'decimal',
            label: 'Quantity',
            default: 1,
            index: true
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        warehouseId: {
            type: 'string',
            label: 'Warehouse',
            index: true
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        orderDate: {
            type: 'date',
            label: 'Order date',
            default: 'date:now',
            index: true
        },
        productionDate: {
            type: 'date',
            label: 'Production date',
            default: 'date:now',
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            index: true,
            required: false
        },
        relatedPartnerId: {
            type: 'string',
            label: 'Related partner',
            index: true,
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },

        // Items
        items: {
            type: [
                {
                    id: {
                        type: 'string',
                        required: false
                    },
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    wastageType: {
                        type: 'string',
                        label: 'Wastage type',
                        default: 'none'
                    },
                    wastage: {
                        type: 'decimal',
                        label: 'Wastage',
                        default: 0
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    calculatedQuantity: {
                        type: 'decimal',
                        label: 'Calculated quantity',
                        default: 1
                    },
                    baseUnitId: {
                        type: 'string',
                        label: 'Base unit'
                    },
                    baseQuantity: {
                        type: 'decimal',
                        label: 'Base Quantity',
                        default: 1
                    },
                    warehouseId: {
                        type: 'string',
                        label: 'Warehouse'
                    },
                    stockQuantity: {
                        type: 'decimal',
                        label: 'Stock on Hand',
                        default: 0
                    },
                    orderedQuantity: {
                        type: 'decimal',
                        label: 'Ordered Quantity',
                        default: 0
                    },
                    assignedQuantity: {
                        type: 'decimal',
                        label: 'Assigned Quantity',
                        default: 0
                    },
                    availableQuantity: {
                        type: 'decimal',
                        label: 'Available Quantity',
                        default: 0
                    },
                    warehouseStockQuantity: {
                        type: 'decimal',
                        label: 'Warehouse Stock on Hand',
                        default: 0
                    },
                    warehouseOrderedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse Ordered Quantity',
                        default: 0
                    },
                    warehouseAssignedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse Assigned Quantity',
                        default: 0
                    },
                    warehouseAvailableQuantity: {
                        type: 'decimal',
                        label: 'Warehouse Available Quantity',
                        default: 0
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    },
                    financialProjectId: {
                        type: 'string',
                        label: 'Project',
                        required: false
                    },
                    additionalItemFields: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    }
                }
            ],
            default: []
        },

        // By-products
        byProducts: {
            type: [
                {
                    id: {
                        type: 'string'
                    },
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    }
                }
            ],
            default: []
        },

        // Services
        services: {
            type: [
                {
                    id: {
                        type: 'string'
                    },
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    }
                }
            ],
            default: []
        },

        // Resources.
        resources: {
            type: [
                {
                    id: {
                        type: 'string'
                    },
                    resourceId: {
                        type: 'string',
                        label: 'Resource'
                    },
                    effort: {
                        type: 'decimal',
                        label: 'Effort (Hour)',
                        default: 0
                    },
                    costType: {
                        type: 'string',
                        label: 'Cost type',
                        default: 'hourly'
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    }
                }
            ],
            default: []
        },

        // Cost
        cost: {
            type: 'decimal',
            label: 'Cost',
            default: 0
        },

        // Stages
        stages: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        stageHistory: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        transferIds: {
            type: ['string'],
            default: [],
            index: true
        },
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        bulkOperationError: {
            type: 'string',
            label: 'Bulk operation error',
            required: false
        },
        workflowApprovalStatus: {
            type: 'string',
            label: 'Workflow approval status',
            required: false,
            index: true
        }
    },
    attributes: {
        stage: {
            collection: 'basic-production.order-stages',
            parentField: 'stageId',
            childField: '_id'
        },
        bom: {
            collection: 'basic-production.bom',
            parentField: 'bomId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        warehouse: {
            collection: 'inventory.warehouses',
            parentField: 'warehouseId',
            childField: '_id'
        },
        unit: {
            collection: 'kernel.units',
            parentField: 'unitId',
            childField: '_id'
        },
        relatedPartner: {
            collection: 'kernel.partners',
            parentField: 'relatedPartnerId',
            childField: '_id'
        },
        financialProject: {
            collection: 'kernel.financial-projects',
            parentField: 'financialProjectId',
            childField: '_id'
        }
    },
     async searchTerms(document) {
            const values = Object.values(_.pick(document, ['code', 'bomId']));
            if (_.isPlainObject(document.bom)) {
                values.push(document.bom.code);
                values.push(document.bom.name);
            }
            return values;
    },
    async copy(document) {
        const app = this.app;
        const numbering = await app.collection('kernel.numbering').findOne({
            code: 'basicProductionOrderNumbering',
            $select: ['_id'],
            $disableInUseCheck: true,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        document.status = 'draft';
        document.recordDate = app.datetime.local().toJSDate();
        document.orderDate = app.datetime.local().toJSDate();
        document.productionDate = app.datetime.local().toJSDate();
        document.progress = 0;
        document.cost = 0;
        document.stages = [];
        document.stageHistory = [];
        document.transferIds = [];
        document.relatedDocuments = [];
        delete document.stageId;
        delete document.code;

        document.code = await app.rpc('kernel.common.request-number', {numberingId: numbering._id, save: true});

        return document;
    },
};
