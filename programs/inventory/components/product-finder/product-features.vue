<template>
    <div class="finder-product-features">
        <el-scrollbar>
            <product-feature
                v-for="feature in productFeatures"
                :key="feature.code"
                :filtered-product-features="filteredProductFeatures"
                :feature="feature"
                @selected="handleSelected"
            />
        </el-scrollbar>

        <div class="features-actions" v-if="filteredProductFeatures.length > 0">
            <el-button type="danger" icon="far fa-broom" @click="$emit('cleared')" />
        </div>
    </div>
</template>

<script>
import ProductFeature from './product-feature.vue';

export default {
    props: {
        productFeatures: Array,
        filteredProductFeatures: Array
    },

    methods: {
        handleSelected(payload) {
            this.$emit('selected', payload);
        }
    },

    components: {
        ProductFeature
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.finder-product-features {
    display: flex;
    flex-flow: row nowrap;
    flex: 0 0 54px;
    width: 100%;
    background-color: $light-50;

    .el-scrollbar {
        display: flex;
        flex-flow: row nowrap;
        flex: 1 1 0;
        width: 100%;
        padding: 12px !important;

        > span {
            margin-right: 12px;

            &:last-of-type {
                margin-right: 0;
            }
        }
    }

    .features-actions {
        display: flex;
        flex-flow: row nowrap;
        padding: 12px;
        align-items: center;
        justify-content: center;
    }
}
</style>
