<template>
    <el-popover
        popper-class="finder-product-feature-popover"
        placement="bottom-start"
        title=""
        width="320"
        trigger="click"
        v-model="isPopoverShown"
    >
        <div slot="reference" class="finder-product-feature">
            <div class="feature-label">
                {{ feature.label }}
            </div>

            <div class="feature-selected-value-count" v-if="currentValues.length > 0">
                {{ currentValues.length }}
            </div>
        </div>

        <div class="product-feature-values">
            <ui-list
                :key="listKey"
                :items="listItems"
                v-model="selectedValues"
                :single-select="false"
                :multi-select-with-click="true"
                enable-search
                enable-compact-search
                enable-apply-clear
                disable-no-rows-overlay
                @applied="handleApply"
                @cleared="handleClear"
            />
        </div>
    </el-popover>
</template>

<script>
import _ from 'lodash';

export default {
    props: {
        filteredProductFeatures: Array,
        feature: Object
    },

    data: () => ({
        listKey: _.uniqueId('listKey_'),
        selectedValues: [],
        isPopoverShown: false
    }),

    computed: {
        listItems() {
            return (this.feature.values || []).map(v => ({_id: v, name: v}));
        },
        currentValues() {
            const filteredProductFeatures = (this.filteredProductFeatures || []).filter(
                f => f.code === this.feature.code
            );
            const values = [];

            for (const fp of filteredProductFeatures) {
                values.push(fp.value);
            }

            return values;
        }
    },

    methods: {
        handleApply() {
            this.$emit('selected', {
                code: this.feature.code,
                label: this.feature.label,
                values: this.selectedValues
            });
            this.isPopoverShown = false;
            setTimeout(() => {
                this.listKey = _.uniqueId('listKey_');
            }, 300);
        },
        handleClear() {
            this.selectedValues = [];
            this.$emit('selected', {
                code: this.feature.code,
                label: this.feature.label,
                values: []
            });
            this.isPopoverShown = false;
            setTimeout(() => {
                this.listKey = _.uniqueId('listKey_');
            }, 300);
        },
        init() {
            const filteredProductFeatures = (this.filteredProductFeatures || []).filter(
                f => f.code === this.feature.code
            );
            const values = [];

            for (const fp of filteredProductFeatures) {
                values.push(fp.value);
            }

            this.selectedValues = values;
        }
    },

    created() {
        this.init();
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.finder-product-feature {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    border: 1px solid $border-color;
    border-radius: 4px;
    padding: 0 7.5px;
    background-color: white;
    cursor: pointer;
    transition: all 0.15s ease-in-out;

    .feature-label {
        font-size: 10px;
        white-space: nowrap;
    }

    .feature-selected-value-count {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: center;
        width: 15px;
        height: 15px;
        margin-left: 5px;
        margin-top: -2px;
        border-radius: 8px;
        background-color: $primary;
        color: white;
        font-size: 7.5px;
    }

    &:hover {
        background-color: $primary-lighter;
        border-color: $primary;
        color: $primary;
    }
}

.finder-product-feature-popover {
    padding: 0;

    .product-feature-values {
        position: relative;
        height: 320px;
        background-color: #fff;
    }
}
</style>
