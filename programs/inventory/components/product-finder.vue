<template>
    <ui-view
        type="content"
        class="inventory-product-finder"
        :title="$t('Products')"
        enable-search
        :left-panel-width="270"
        :actions="actions"
        @completed-selection="handleCompleteSelection"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                :id="`inventory.product-finder-${$params('module')}`"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />

            <div class="price-list-select" v-if="priceLists.length > 0">
                <div class="price-list-select-label">{{ $t('Price list') }}</div>
                <el-select v-model="priceListId" filterable>
                    <el-option
                        v-for="priceList in priceLists"
                        :key="priceList._id"
                        :label="priceList.name"
                        :value="priceList._id"
                    />
                </el-select>
            </div>
        </template>
        <template slot="left-panel">
            <div class="product-categories">
                <div class="category-search">
                    <el-input
                        v-model="categorySearchQuery"
                        :placeholder="'Search category..' | t"
                        prefix-icon="el-icon-search"
                        autocorrect="off"
                        autocapitalize="off"
                        spellcheck="false"
                        clearable
                        size="medium"
                        @input="categorySearchQuery = $event"
                    />
                </div>
                <ui-table
                    ref="table"
                    :items="categories"
                    :columns="categoriesColumns"
                    :search="categorySearchQuery"
                    :enable-row-handle="false"
                    no-zebra
                    :options="categoriesTableOptions"
                    @selected="handleSelectCategory"
                    v-if="categories.length > 0"
                />
            </div>
        </template>

        <div class="finder-wrapper">
            <finder-product-features
                :product-features="productFeatures"
                :filtered-product-features="filteredProductFeatures"
                @selected="handleFeatureSelect"
                @cleared="handleFeaturesClear"
                v-if="productFeatures.length > 0"
            />

            <div class="finder-products">
                <ui-table
                    :key="productsKey"
                    collection="inventory.products"
                    :id="`inventory.product-finder-${$params('module')}`"
                    :search-text="$params('search')"
                    :filters="filters"
                    :columns="columns"
                    :extra-fields="['categoryPath', 'isConfigurable', 'additionalInformation', 'lastCost']"
                    :enable-selection="false"
                    :enable-text-search="true"
                    :process-result="processProducts"
                    :size-to-fit="sizeToFitProductsTable"
                    @row-clicked="handleSelect"
                />
            </div>

            <div class="finder-selected" v-if="selected.length > 0">
                <ui-table
                    :items="selected"
                    :schema="selectedSchema"
                    :min-empty-rows="0"
                    enable-editing
                    @changed="handleSelectedChange"
                />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {escapeRegExp, toLower, toUpper} from 'framework/helpers';
import {
    getAdditionalInformationColumns,
    getAdditionalInformationScopeFilters
} from '../../system/helpers/additional-information';

import FinderProductFeatures from './product-finder/product-features';

export default {
    data: () => ({
        scopeQuery: {},
        categorySearchQuery: '',
        categories: [],
        categoriesColumns: [
            {
                field: 'name',
                label: 'Category',
                cellRenderer: 'agGroupCellRenderer',
                showRowGroup: true,
                suppressMenu: true,
                suppressMovable: true,
                cellRendererParams: {
                    suppressCount: true,
                    suppressDoubleClickExpand: true,
                    innerRenderer(params) {
                        const data = params.data;

                        if (_.isObject(data)) {
                            return data.name;
                        }
                    }
                },
                getQuickFilterText(params) {
                    const data = params.data;

                    if (_.isObject(data)) {
                        return `${data.code} - ${data.name}`;
                    }
                }
            }
        ],
        categoriesTableOptions: {
            treeData: true,
            groupDefaultExpanded: 1,
            groupSuppressAutoColumn: true,
            getDataPath(data) {
                return data.tree.path.split('/');
            },
            suppressContextMenu: true
        },
        categoryFilters: {},
        priceLists: [],
        priceListId: null,
        selected: [],
        productsKey: _.uniqueId('productsKey_'),
        additionalInformationColumns: [],
        additionalInformationScopeFilters: [],
        productFeatures: [],
        filteredProductFeatures: [],
        sizeToFitProductsTable: true,
        initialLoad: true,
        isInitialized: false
    }),

    computed: {
        actions() {
            return [
                {
                    name: 'completed-selection',
                    title: 'Select',
                    icon: 'check',
                    disabled: () => {
                        return this.selected.length < 1;
                    }
                },
                {
                    name: 'cancel',
                    title: 'Cancel',
                    plain: true,
                    icon: 'times',
                    isCancel: true
                }
            ];
        },
        filters() {
            const query = fastCopy(this.scopeQuery);
            const filteredProductFeatures = fastCopy(this.filteredProductFeatures);

            if (_.isPlainObject(this.$params('filters')) && !_.isEmpty(this.$params('filters'))) {
                if (!Array.isArray(query.$and)) query.$and = [];

                query.$and.push(this.$params('filters'));
            }

            if (!_.isEmpty(this.categoryFilters)) {
                if (!Array.isArray(query.$and)) query.$and = [];

                query.$and.push(fastCopy(this.categoryFilters));
            }

            if (Array.isArray(filteredProductFeatures) && filteredProductFeatures.length > 0) {
                if (!Array.isArray(query.$and)) query.$and = [];

                const grouped = _.groupBy(filteredProductFeatures, 'code');
                for (const code of Object.keys(grouped)) {
                    query.$and.push({
                        [`features.${code}`]: {$in: grouped[code].map(g => g.value)}
                    });
                }
            }

            return query;
        },
        columns() {
            const additionalInformationColumns = this.additionalInformationColumns || [];

            return [
                {
                    field: 'code',
                    label: 'Code',
                    width: 120,
                    visibleForExport: true,
                    selectedForExport: true
                },
                {
                    field: 'definition',
                    label: 'Definition',
                    exportField: 'definition'
                },
                {field: 'barcode', label: 'Barcode', width: 120, visible: false},
                {
                    field: 'category',
                    label: 'Category',
                    valueGetter(params) {
                        if (_.isObject(params.data) && !params.node.group) {
                            return params.data.categoryPath;
                        }

                        return '';
                    },
                    visible: false
                },
                {
                    field: 'type',
                    label: 'Type',
                    valueLabels: [
                        {value: 'stockable', label: 'Stockable product'},
                        {value: 'service', label: 'Service product'}
                    ],
                    width: 150,
                    visible: false
                },
                {
                    field: 'brand.name',
                    label: 'Brand',
                    width: 150,
                    selectedForExport: true
                },
                {
                    field: 'manufacturer',
                    label: 'Manufacturer',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.manufacturer) &&
                            _.isString(data.manufacturer.code) &&
                            _.isString(data.manufacturer.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.manufacturerId;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    width: 180,
                    visible: false
                },
                {field: 'manufacturerProductCode', label: 'Manufacturer product code', width: 150, visible: false},
                {
                    field: 'baseUnit.name',
                    label: 'Unit',
                    maxWidth: 120,
                    visible: false
                },
                {
                    field: 'stockQuantity',
                    label: 'Stock on Hand',
                    format: 'unit',
                    width: 150,
                    visible: false,
                    hidden: !this.$params('warehouseId')
                },
                {
                    field: 'orderedQuantity',
                    label: 'Ordered Quantity',
                    format: 'unit',
                    width: 150,
                    visible: false,
                    hidden: !this.$params('warehouseId')
                },
                {
                    field: 'assignedQuantity',
                    label: 'Assigned Quantity',
                    format: 'unit',
                    width: 150,
                    visible: false,
                    hidden: !this.$params('warehouseId')
                },
                {
                    field: 'availableQuantity',
                    label: 'Available Quantity',
                    format: 'unit',
                    width: 150,
                    hidden: !this.$params('warehouseId')
                },
                {
                    field: 'price',
                    label: 'Price',
                    width: 150,
                    format: 'currency',
                    formatOptions: () => this.currencyFormat,
                    hidden: !this.priceListId
                },
                {field: 'updatedAt', hidden: true, sort: 'desc'},
                ...additionalInformationColumns
            ];
        },
        scopeApplicableFilters() {
            const self = this;
            const additionalInformationScopeFilters = this.additionalInformationScopeFilters || [];

            return [
                {label: 'Is Active', query: {isActive: {$ne: false}}, type: 'boolean'},
                {label: 'Is Passive', query: {isActive: false}, type: 'boolean'},
                {label: 'Allow negative stock', query: {negativeStock: true}, type: 'boolean'},

                {
                    field: 'groupIds',
                    label: 'Group',
                    collection: 'inventory.product-groups'
                },
                {
                    field: 'categoryPath',
                    label: 'Category',
                    collection: 'inventory.product-categories',
                    valueFrom: 'path',
                    labelFrom: 'path',
                    operator: 'starts-with',
                    condition() {
                        return !self.hasCategoriesFilter;
                    }
                },
                {field: 'barcode', label: 'Barcode'},
                {
                    field: 'type',
                    label: 'Type',
                    translateLabels: true,
                    items: [
                        {value: 'stockable', label: 'Stockable product'},
                        {value: 'service', label: 'Service product'}
                    ],
                    condition() {
                        return !(!!self.$params('filters.isConfigurable') || !!self.$params('filters.isKit'));
                    }
                },
                {
                    field: 'brandId',
                    label: 'Brand',
                    collection: 'inventory.product-brands'
                },
                {
                    field: 'manufacturerId',
                    label: 'Manufacturer',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'vendor', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {field: 'manufacturerProductCode', label: 'Manufacturer product code'},
                {field: 'canBeSold', label: 'Can be sold', type: 'boolean'},
                {field: 'canBePurchased', label: 'Can be purchased', type: 'boolean'},
                {field: 'canBeExpensed', label: 'Can be expensed', type: 'boolean'},
                {field: 'isConsumable', label: 'Is consumable', type: 'boolean'},
                {field: 'isECommerceProduct', label: 'Is e-commerce product', type: 'boolean'},
                ...additionalInformationScopeFilters
            ];
        },
        selectedSchema() {
            return {
                code: {
                    type: 'string',
                    label: 'Code',
                    column: {
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },
                definition: {
                    type: 'string',
                    label: 'Definition',
                    column: {
                        minWidth: 210
                    },
                    editor: {
                        disabled: true
                    }
                },
                unitName: {
                    type: 'string',
                    label: 'Unit',
                    column: {
                        width: 120
                    },
                    editor: {
                        disabled: true
                    }
                },
                quantity: {
                    type: 'decimal',
                    label: 'Quantity',
                    min: 0,
                    column: {
                        width: 150,
                        format: 'unit',
                        formatOptions: () => ({
                            number: {
                                precision: this.$setting('system.unitPrecision')
                            }
                        })
                    }
                }
            };
        }
    },

    watch: {
        async priceListId(value, oldValue) {
            if (value !== oldValue && !!value && !this.initialLoad) {
                const company = this.$store.getters['session/company'];
                const priceList = this.priceLists.find(pl => pl._id === value);

                if (company.currencyId !== priceList.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: priceList.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };
                    }
                }

                this.productsKey = _.uniqueId('productsKey_');
            }
        }
    },

    methods: {
        handleFeatureSelect(payload) {
            const filteredProductFeatures = fastCopy(this.filteredProductFeatures).filter(f => f.code !== payload.code);

            for (const v of payload.values || []) {
                filteredProductFeatures.push({
                    ..._.omit(payload, 'values'),
                    value: v
                });
            }

            this.sizeToFitProductsTable = false;
            this.filteredProductFeatures = filteredProductFeatures;
        },
        handleFeaturesClear() {
            this.sizeToFitProductsTable = false;
            this.filteredProductFeatures = [];
        },
        handleCompleteSelection() {
            if (this.selected.length < 1) return;

            this.$params('onSelect')(this.selected);

            this.$dialog.close();
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handleSelectCategory(selected) {
            const query = {$or: []};

            for (const category of selected) {
                query.$or.push({
                    categoryPath: {
                        $regex: `^${escapeRegExp(category.path)}/`,
                        $options: 'i'
                    }
                });
                query.$or.push({
                    categoryPath: {
                        $regex: `^${escapeRegExp(category.path)}$`,
                        $options: 'i'
                    }
                });
                // query.$or.push({
                //     categoryPath: {
                //         $regex: `^${category.path}`,
                //         $options: 'i'
                //     }
                // });
                // query.$or.push(...[
                //     {
                //         'categoryPath': {
                //             $regex: `^${toUpper(escapeRegExp(category.path))}`,
                //             $options: 'i'
                //         }
                //     },
                //     {
                //         'categoryPath': {
                //             $regex: `^${toLower(escapeRegExp(category.path))}`,
                //             $options: 'i'
                //         }
                //     }
                // ]);
            }

            if (query.$or.length < 1) {
                delete query.$or;
            }

            this.categoryFilters = query;
        },
        handleSelect(row) {
            const selected = fastCopy(this.selected);
            const existingIndex = selected.findIndex(sl => sl._id === row._id);

            this.sizeToFitProductsTable = false;

            if (existingIndex === -1) {
                const s = {};

                s._id = row._id;
                s.code = row.code;
                s.definition = row.definition;
                s.unitId = row.baseUnitId;
                s.unitName = row.baseUnit.name;
                s.lastCost = row.lastCost;
                s.quantity = 1;

                selected.push(s);
            } else {
                selected[existingIndex].quantity += 1;
            }

            this.selected = selected;

            setTimeout(() => {
                this.sizeToFitProductsTable = true;
            }, 150);
        },
        handleSelectedChange(selected) {
            this.sizeToFitProductsTable = false;
            this.selected = selected;

            setTimeout(() => {
                this.sizeToFitProductsTable = true;
            }, 150);
        },
        async processProducts(result) {
            const priceListId = this.priceListId;
            if (!!priceListId && _.isDate(this.$params('issueDate'))) {
                if (this.$params('module') === 'sale') {
                    const prices = await this.$collection('sale.product-prices').find({
                        productId: {$in: result.data.map(p => p._id)},
                        priceListId,
                        validFrom: {
                            $lte: this.$params('issueDate')
                        },
                        validTo: {
                            $gte: this.$params('issueDate')
                        }
                    });

                    if (prices.length > 0) {
                        for (const product of result.data) {
                            const productPrice = prices.find(price => price.productId === product._id);

                            product.price = !!productPrice ? productPrice.price : 0;
                        }
                    }
                } else if (this.$params('module') === 'purchase') {
                    const prices = await this.$collection('purchase.product-prices').find({
                        productId: {$in: result.data.map(p => p._id)},
                        listPriceId: priceListId,
                        validFrom: {
                            $lte: this.$params('issueDate')
                        },
                        validTo: {
                            $gte: this.$params('issueDate')
                        }
                    });

                    if (prices.length > 0) {
                        for (const product of result.data) {
                            const productPrice = prices.find(price => price.productId === product._id);

                            product.price = !!productPrice ? productPrice.price : 0;
                        }
                    }
                }
            }

            const warehouseId = this.$params('warehouseId');
            if (!!warehouseId && _.isDate(this.$params('issueDate'))) {
                const stockQuery = {
                    date: this.$params('issueDate'),
                    warehouseId,
                    productId: result.data.map(p => p._id)
                };
                const report = await this.$rpc('inventory.get-stock-report', stockQuery);

                if (Array.isArray(report) && report.length > 0) {
                    for (const product of result.data) {
                        const r = report.find(r => r.productId === product._id);

                        if (!!r) {
                            product.stockQuantity = r.stockQuantity;
                            product.orderedQuantity = r.orderedQuantity;
                            product.assignedQuantity = r.assignedQuantity;
                            product.availableQuantity = r.availableQuantity;
                        } else {
                            product.stockQuantity = 0;
                            product.orderedQuantity = 0;
                            product.assignedQuantity = 0;
                            product.availableQuantity = 0;
                        }
                    }
                }
            }

            if (!this.initialLoad) {
                this.productFeatures = await this.$rpc('inventory.product-finder-get-product-features', {
                    filters: this.filters,
                    searchText: this.searchText,
                    filteredProductFeatures: this.filteredProductFeatures
                });
            }

            this.initialLoad = false;

            setTimeout(() => {
                this.sizeToFitProductsTable = true;
            }, 300);

            return result;
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];

        this.currencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            }
        };
        this.categories = await this.$collection('inventory.product-categories').find();

        if (this.$params('module') === 'sale') {
            const query = {
                $select: ['_id', 'name', 'currencyId']
            };

            if (Array.isArray(this.$params('allowedPriceListIds'))) {
                query._id = {$in: this.$params('allowedPriceListIds')};
            }

            this.priceLists = await this.$collection('sale.price-lists').find(query);
        } else if (this.$params('module') === 'purchase') {
            const query = {
                $select: ['_id', 'name', 'currencyId']
            };

            if (Array.isArray(this.$params('allowedPriceListIds'))) {
                query._id = {$in: this.$params('allowedPriceListIds')};
            }

            this.priceLists = await this.$collection('purchase.list-prices').find(query);
        }
        if (this.priceLists.length > 0) {
            this.priceListId = this.priceLists[0]._id;

            if (!!this.$params('priceListId')) {
                this.priceListId = this.$params('priceListId');
            }

            const priceList = this.priceLists.find(pl => pl._id === this.priceListId);
            if (!!priceList) {
                if (company.currencyId !== priceList.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: priceList.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };
                    }
                }
            } else {
                this.priceListId = null;
            }
        }

        const ais = await this.$collection('kernel.additional-information').find({
            type: {$in: ['stockable-product', 'service-product']}
        });
        const additionalInformationColumns = [];
        const additionalInformationScopeFilters = [];
        for (const ai of ais) {
            const columns = getAdditionalInformationColumns(ai);
            for (const column of columns) {
                if (additionalInformationColumns.findIndex(c => c.field === column.field) === -1) {
                    additionalInformationColumns.push(column);
                }
            }

            const filters = getAdditionalInformationScopeFilters(ai);
            for (const filter of filters) {
                if (additionalInformationScopeFilters.findIndex(f => f.field === filter.field) === -1) {
                    additionalInformationScopeFilters.push(filter);
                }
            }
        }
        this.additionalInformationColumns = additionalInformationColumns;
        this.additionalInformationScopeFilters = additionalInformationScopeFilters;

        this.productFeatures = await this.$rpc('inventory.product-finder-get-product-features', {
            filters: this.filters
        });

        this.isInitialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    },

    components: {
        FinderProductFeatures
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.inventory-product-finder {
    .finder-wrapper {
        display: flex;
        flex-flow: column nowrap;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .finder-products {
            flex: 1 1 0;

            .ag-row {
                cursor: pointer;
            }
        }

        .finder-selected {
            flex: 0 0 40%;
        }
    }

    .product-categories {
        position: relative;
        display: flex;
        flex-flow: column nowrap;
        flex: 0 0 300px;
        min-width: 210px;
        height: 100%;
        background-color: #fff;

        .category-search {
            width: 100%;
            flex: 0 0 30px;

            .el-input__inner {
                border-radius: 0;
                border: none;
                border-bottom: 1px solid $border-color;
            }
        }

        .ui-table {
            flex: 1 1 0;
        }

        .ag-pinned-left-cols-container,
        .ag-pinned-left-header,
        .ag-header-viewport,
        .ag-pinned-right-header,
        .ag-header,
        .ag-header-row {
            display: none !important;
        }
    }

    .ui-view-wrapper {
        padding-top: 45px !important;
    }

    .ui-view-top-panel {
        display: flex;
        height: 45px !important;
        padding: 7.5px;

        .ui-scope {
            height: 30px;
            margin-right: 5px;

            .scope-applied-items {
                height: 30px;

                .remove-applied-items {
                    top: 2.5px;
                }
            }
        }

        .price-list-select {
            display: flex;
            flex-flow: row nowrap;
            width: 450px;
            justify-content: flex-end;
            align-items: center;

            .price-list-select-label {
                margin-right: 10px;
                white-space: nowrap;
            }
        }
    }
}
</style>
