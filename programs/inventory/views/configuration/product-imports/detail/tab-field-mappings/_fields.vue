<template>
    <div class="mappings-fields">
        <ui-list
            :items="items"
            value-from="field"
            label-from="label"
            :single-select="false"
            enable-search
            multi-select-with-click
            :html-template="htmlTemplate"
            @selected="$emit('selected', $event)"
        />
    </div>
</template>

<script>
import _ from 'lodash';

export default {
    props: {
        fields: Array
    },

    computed: {
        items() {
            return _.cloneDeep(this.fields).map(field => {
                if (field.field === 'identifier') {
                    return {
                        field: 'identifier',
                        label: this.$t('Identifier')
                    };
                }

                return {
                    field: field.field,
                    label: this.$t(field.label)
                };
            });
        }
    },

    methods: {
        htmlTemplate(data) {
            return `<i class="fas fa-database text-success mr5"></i>${data.label}`;
        }
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.inventory-product-imports-tab-field-mappings .mappings-fields {
    flex: 0 0 240px;

    .ui-list {
        .ui-table .ag-body-viewport.ps {
            padding: 10px 0;
        }

        .ag-theme-balham.ui-table .ag-row {
            border-bottom-width: 0;
        }

        .ag-theme-balham .ag-cell,
        .ag-theme-balham .ag-full-width-row .ag-cell-wrapper.ag-row-group {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }
    }
}
</style>
