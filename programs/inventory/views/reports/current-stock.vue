<template>
    <ui-view
        ref="view"
        type="table"
        collection="inventory.quantities"
        :columns="columns"
        :filters="filters"
        :group-by="scopeGroups"
        :extra-fields="['productId']"
        :enable-selection="false"
        :enable-search="true"
        :enable-search-for-list="false"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="inventory.reports.current-stock"
                :filters="scopeApplicableFilters"
                :groups="scopeApplicableGroups"
                :applied-items="appliedScopeItems"
                @changed="handleScopeChange"
            />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {escapeRegExp, firstUpper, toLower, toUpper, trim} from 'framework/helpers';

export default {
    data: () => ({
        scopeQuery: {},
        scopeGroups: [],
        userWarehouseIds: [],
        isInitialized: false,
        search: ''
    }),

    computed: {
        filters() {
            const search = this.search;
            let filters = _.cloneDeep(this.scopeQuery);
            let warehouseIds = [];

            if (_.isObject(filters.warehouseId) && Array.isArray(filters.warehouseId.$in)) {
                warehouseIds = filters.warehouseId.$in;
            } else if (Array.isArray(filters.$and)) {
                filters.$and.forEach(q => {
                    if (_.isObject(q.warehouseId) && Array.isArray(q.warehouseId.$in)) {
                        warehouseIds = q.warehouseId.$in;
                    }
                });
            }
            if (warehouseIds.length < 1) {
                if (!Array.isArray(filters.$and)) filters.$and = [];

                filters.$and.push({warehouseId: {$in: this.userWarehouseIds}});
            }

            if (!!search) {
                filters = _.cloneDeep(filters);

                if (!Array.isArray(filters.$or)) {
                    filters.$or = [];
                }

                filters.$or.push(
                    ...[
                        {'product.code': {$regex: toUpper(escapeRegExp(search)), $options: 'i'}},
                        {'product.code': {$regex: toLower(escapeRegExp(search)), $options: 'i'}},
                        {'product.code': {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}},

                        {'product.definition': {$regex: toUpper(escapeRegExp(search)), $options: 'i'}},
                        {'product.definition': {$regex: toLower(escapeRegExp(search)), $options: 'i'}},
                        {'product.definition': {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}}
                    ]
                );
            }

            return filters;
        },
        columns() {
            return [
                {field: 'product.displayName', hidden: true},
                {field: 'warehouse.name', hidden: true},
                {field: 'location.path', hidden: true},
                {field: 'lotNumber', hidden: true},

                {
                    field: 'productCode',
                    label: 'Product Code',
                    visible: false,
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isObject(params.data.product)) {
                            return params.data.product.code;
                        }
                        return '';
                    },
                    minWidth: 120
                },
                  {
                    field: 'productName',
                    label: 'Product Name',
                    visible: false,
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isObject(params.data.product)) {
                            return params.data.product.definition;
                        }
                        return '';
                    },
                    minWidth: 120
                },
                {
                    field: 'product',
                    label: 'Product',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            !params.node.group && _.isObject(data.product) && _.isString(data.product.displayName);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products-detail';
                            relation.id = data.productId;
                            relation.template = '{{displayName}}';
                        }

                        return relation;
                    },
                    minWidth: 240
                },
                {
                    field: 'warehouse',
                    label: 'Warehouse',
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isObject(params.data.warehouse) && !params.node.group) {
                            return params.data.warehouse.shortName + ' - ' + params.data.warehouse.name;
                        }

                        return '';
                    },
                    width: 180
                },
                {
                    field: 'location',
                    label: 'Location',
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isObject(params.data.location) && !params.node.group) {
                            return params.data.location.path;
                        }

                        return '';
                    },
                    hidden: !this.$app.setting('inventory.storageLocations'),
                    width: 180
                },
                {
                    field: 'serialNumber',
                    label: 'Serial Number',
                    hidden: !this.$app.setting('inventory.trackBySerialOrLot'),
                    width: 150
                },
                {
                    field: 'lot',
                    label: 'Lot Number',
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isString(params.data.lotNumber) && !params.node.group) {
                            return params.data.lotNumber;
                        }

                        return '';
                    },
                    hidden: !this.$app.setting('inventory.trackBySerialOrLot'),
                    width: 150
                },
                {
                    field: 'specialQty',
                    label: 'Special quantity',
                    format: 'unit',
                    hidden: !this.$app.setting('inventory.specialUnits'),
                    width: 150
                },
                {
                    field: 'specialUnit.name',
                    label: 'Special unit',
                    hidden: !this.$app.setting('inventory.specialUnits'),
                    width: 120
                },
                {
                    field: 'qty',
                    label: 'Quantity',
                    format: 'unit',
                    width: 150
                },
                {
                    field: 'unit.name',
                    label: 'Unit',
                    width: 120
                }
            ];
        },
        appliedScopeItems() {
            const applied = [
                {type: 'filter', payload: 'internal-locations'},
                {type: 'group', payload: 'group-by-product'}
            ];

            if (this.$setting('inventory.storageLocations')) {
                applied.push({type: 'group', payload: 'group-by-location'});
            }

            return applied;
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                // Pre-defined
                {code: 'internal-locations', label: 'Internal locations', query: {'location.type': 'internal'}},
                {label: 'Transit locations', query: {'location.type': 'transit'}},
                {label: 'Negative stock', query: {qty: {$lt: 0}}},
                {label: 'Positive stock', query: {qty: {$gt: 0}}},

                // Fields.
                {
                    field: 'productId',
                    label: 'Product',
                    collection: 'inventory.products',
                    labelFrom: 'displayName',
                    filters: {$sort: {code: 1}, isSimple: true, type: {$ne: 'service'}}
                },
                {
                    field: 'productCategoryPath',
                    label: 'Product category',
                    collection: 'inventory.product-categories',
                    valueFrom: 'path',
                    labelFrom: 'path',
                    operator: 'starts-with'
                },
                {
                    field: 'warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    extraFields: ['shortName'],
                    template: '{{shortName}} - {{name}}'
                },
                {
                    field: 'location.path',
                    label: 'Location',
                    collection: 'inventory.locations',
                    valueFrom: 'path',
                    labelFrom: 'path',
                    operator: 'starts-with',
                    condition() {
                        return self.$setting('inventory.storageLocations');
                    }
                },
                {
                    field: 'serialNumber',
                    label: 'Serial number',
                    condition() {
                        return self.$setting('inventory.trackBySerialOrLot');
                    }
                },
                {
                    field: 'lotNumber',
                    label: 'Lot number',
                    condition() {
                        return self.$setting('inventory.trackBySerialOrLot');
                    }
                },
                {field: 'qty', label: 'Quantity', type: 'integer'}
            ];
        },
        scopeApplicableGroups() {
            return [
                {
                    code: 'group-by-product',
                    field: 'product.displayName',
                    label: 'Product',
                    aggregate: {
                        qty: 'sum',
                        specialQty: 'sum'
                    }
                },
                {
                    code: 'group-by-warehouse',
                    field: 'warehouse.name',
                    label: 'Warehouse',
                    aggregate: {
                        qty: 'sum',
                        specialQty: 'sum'
                    }
                },
                {
                    code: 'group-by-location',
                    field: 'location.path',
                    label: 'Location',
                    aggregate: {
                        qty: 'sum',
                        specialQty: 'sum'
                    }
                },
                {
                    field: 'lotNumber',
                    label: 'Lot number',
                    aggregate: {
                        qty: 'sum',
                        specialQty: 'sum'
                    }
                }
            ];
        }
    },

    watch: {
        '$program.definition.params.search'(value) {
            this.search = trim(value);
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
            this.scopeGroups = model.groups;
        }
    },

    async created() {
        const user = this.$user;
        this.userWarehouseIds = (
            await this.$collection('inventory.warehouses').find({
                branchId: {$in: user.branchIds},
                $select: ['_id']
            })
        ).map(warehouse => warehouse._id);

        this.isInitialized = true;
    }
};
</script>
