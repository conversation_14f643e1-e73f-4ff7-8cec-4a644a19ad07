import _ from 'lodash';
import {getMongoSearchQuery, toLower, toUpper} from 'framework/helpers';
import fastCopy from 'fast-copy';

let allFeatures = {};

export default [
    {
        name: 'product-finder-get-product-features',
        async action(payload, params) {
            const app = this.app;
            const {filters, searchText, filteredProductFeatures} = payload;

            const query = {...filters};
            if (typeof searchText === 'string' && searchText.trim() !== '') {
                const searchMethod = app.setting('system.searchMethod');

                if (searchMethod === 'full-text') {
                    if (!Array.isArray(query.$and)) query.$and = [];

                    query.$and.push({
                        $text: {
                            $search: `${toLower(searchText)
                                .replaceAll('/', ' ')
                                .replaceAll('-', ' ')
                                .replaceAll(' - ', ' ')
                                .replaceAll(' -', ' ')
                                .replaceAll('- ', ' ')
                                .replaceAll('_', ' ')
                                .replaceAll('.', ' ')}`
                        }
                    });
                    if (searchText.indexOf(' ') !== -1) {
                        const parts = searchText.split(' ');

                        for (const searchText of parts) {
                            query.$and.push(getMongoSearchQuery('searchText', toLower(searchText)));
                        }
                    } else {
                        query.$and.push(getMongoSearchQuery('searchText', searchText));
                    }
                } else {
                    if (!Array.isArray(query.$and)) query.$and = [];

                    if (this.searchText.indexOf(' ') !== -1) {
                        const parts = searchText.split(' ');

                        for (const searchText of parts) {
                            query.$and.push(getMongoSearchQuery('searchText', toLower(searchText)));
                        }
                    } else {
                        query.$and.push(getMongoSearchQuery('searchText', searchText));
                    }
                }
            }

            const report = await app.collection('inventory.products').aggregate([
                {
                    $match: query
                },
                {
                    $group: {
                        _id: '$features'
                    }
                }
            ]);

            const featureSets = await app.collection('inventory.product-feature-sets').find({
                $select: ['_id', 'fields.code', 'fields.label']
            });
            const featuresMap = {};
            for (const featureSet of featureSets) {
                for (const field of featureSet.fields ?? []) {
                    featuresMap[field.code] = {
                        code: field.code,
                        label: toUpper(field.label),
                        values: []
                    };
                }
            }

            for (const r of report) {
                for (const key of Object.keys(r._id ?? {})) {
                    const value = r._id[key];

                    if (
                        ((typeof value === 'string' && value !== '') || typeof value === 'number') &&
                        typeof featuresMap[key] !== 'undefined' &&
                        !featuresMap[key].values.includes(value)
                    ) {
                        featuresMap[key].values.push(value);
                    } else if (Array.isArray(value) && value.length > 0) {
                        for (const v of value) {
                            if (
                                ((typeof v === 'string' && v !== '') || typeof v === 'number') &&
                                typeof featuresMap[key] !== 'undefined' &&
                                !featuresMap[key].values.includes(v)
                            ) {
                                featuresMap[key].values.push(v);
                            }
                        }
                    }
                }
            }

            if (_.isEmpty(allFeatures)) {
                allFeatures = fastCopy(featuresMap);
            } else {
                for (const code of Object.keys(featuresMap)) {
                    if (typeof allFeatures[code] === 'undefined') {
                        allFeatures[code] = fastCopy(featuresMap[code]);
                    } else {
                        for (const value of featuresMap[code].values) {
                            if (!allFeatures[code].values.includes(value)) {
                                allFeatures[code].values.push(value);
                            }
                        }
                    }
                }
            }

            return Object.values(featuresMap).filter(feature => {
                if ((filteredProductFeatures ?? []).some(f => f.code === feature.code)) {
                    const allValues = allFeatures[feature.code]?.values ?? [];

                    if (Array.isArray(allValues) && allValues.length > 0) {
                        feature.values = allValues;
                    }

                    return feature.values.length > 0;
                }

                return feature.values.length > 1;
            });
        }
    }
];
