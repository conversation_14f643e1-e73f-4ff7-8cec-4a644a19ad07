import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'products',
    title: 'Products',
    view: 'inventory.catalog.products',
    labelParams: {
        from: 'displayName'
    },
    extraIndexes: [
        {type: 'normal', key: 'attributes.*', value: 1},
        {type: 'normal', key: 'features.*', value: 1},
        {type: 'normal', key: 'additionalInformation.*', value: 1}
    ],
    schema: {
        // General
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        nameTranslations: {
            type: [
                {
                    languageId: {
                        type: 'string',
                        label: 'Language'
                    },
                    languageName: {
                        type: 'string',
                        label: 'Language name'
                    },
                    translation: {
                        type: 'string',
                        label: 'Translation'
                    }
                }
            ],
            default: []
        },
        contentTranslations: {
            type: [
                {
                    languageId: {
                        type: 'string',
                        label: 'Language'
                    },
                    languageName: {
                        type: 'string',
                        label: 'Language name'
                    },
                    translation: {
                        type: 'string',
                        label: 'Translation'
                    }
                }
            ],
            default: []
        },
        descriptionTranslations: {
            type: [
                {
                    languageId: {
                        type: 'string',
                        label: 'Language'
                    },
                    languageName: {
                        type: 'string',
                        label: 'Language name'
                    },
                    translation: {
                        type: 'string',
                        label: 'Translation'
                    }
                }
            ],
            default: []
        },
        displayName: {
            type: 'string',
            label: 'Display name',
            required: false,
            index: true
        },
        definition: {
            type: 'string',
            label: 'Definition',
            required: false,
            index: true
        },
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['stockable', 'service', 'kit'],
            default: 'stockable',
            index: true
        },
        categoryId: {
            type: 'string',
            label: 'Category',
            index: true
        },
        categoryPath: {
            type: 'string',
            required: false,
            index: true
        },
        groupIds: {
            type: ['string'],
            label: 'Group',
            index: true
        },
        barcode: {
            type: 'string',
            label: 'Barcode',
            required: false,
            unique: true,
            index: true
        },
        baseUnitId: {
            type: 'string',
            label: 'Base unit',
            index: true
        },
        specialUnitId: {
            type: 'string',
            label: 'Special unit',
            required: false
        },
        specialUnitFormula: {
            type: 'string',
            label: 'Special unit',
            required: false
        },
        description: {
            type: 'string',
            label: 'Short description',
            required: false
        },
        image: {
            type: 'string',
            label: 'Image',
            required: false
        },
        tagIds: {
            type: ['string'],
            label: 'Tags',
            default: []
        },
        brandId: {
            type: 'string',
            label: 'Brand',
            required: false,
            index: true
        },
        shippingUnitId: {
            type: 'string',
            label: 'Shipping unit',
            required: false
        },
        typeOfGoodsId: {
            type: 'string',
            label: 'Type of goods',
            required: false
        },
        hsCode: {
            type: 'string',
            label: 'HS code',
            required: false,
            index: true
        },
        manufacturerId: {
            type: 'string',
            label: 'Manufacturer',
            required: false,
            index: true
        },
        manufacturerProductCode: {
            type: 'string',
            label: 'Manufacturer product code',
            required: false,
            index: true
        },
        countryOfManufactureId: {
            type: 'string',
            label: 'Country of manufacture',
            required: false
        },
        classificationCode: {
            type: 'string',
            label: 'Classification code',
            required: false
        },
        classificationVersion: {
            type: 'string',
            label: 'Classification version',
            required: false
        },
        classificationValue: {
            type: 'string',
            label: 'Classification value',
            required: false
        },
        countryOfOriginId: {
            type: 'string',
            label: 'Country of origin',
            required: false
        },
        containerTypeId: {
            type: 'string',
            label: 'Container type',
            required: false
        },
        containerNo: {
            type: 'string',
            label: 'Container no',
            required: false
        },
        containerBrand: {
            type: 'string',
            label: 'Container brand',
            required: false
        },
        // containerQuantity: {
        //     type: 'decimal',
        //     label: 'Container quantity',
        //     required: false
        // },
        canBeSold: {
            type: 'boolean',
            label: 'Can be sold',
            default: true,
            index: true
        },
        canBePurchased: {
            type: 'boolean',
            label: 'Can be purchased',
            default: true,
            index: true
        },
        canBeProduced: {
            type: 'boolean',
            label: 'Can be produced',
            default: false,
            index: true
        },
        canBeInspected: {
            type: 'boolean',
            label: 'Can be inspected',
            default: false,
            index: true
        },
        canBeExpensed: {
            type: 'boolean',
            label: 'Can be expensed',
            default: false,
            index: true
        },
        isConsumable: {
            type: 'boolean',
            label: 'Is consumable',
            default: false,
            index: true
        },
        isFixture: {
            type: 'boolean',
            label: 'Is fixture',
            default: false,
            index: true
        },
        isServiceProduct: {
            type: 'boolean',
            label: 'Is service product',
            default: false,
            index: true
        },
        isECommerceProduct: {
            type: 'boolean',
            label: 'Is e-commerce product',
            default: false,
            index: true
        },
        isSimple: {
            type: 'boolean',
            label: 'Is simple',
            default: true,
            index: true
        },
        isVariant: {
            type: 'boolean',
            label: 'Is variant',
            default: false,
            index: true
        },
        isConfigurable: {
            type: 'boolean',
            label: 'Is configurable',
            default: false,
            index: true
        },
        isKit: {
            type: 'boolean',
            label: 'Is kit product',
            default: false,
            index: true
        },
        useSubProducts: {
            type: 'boolean',
            label: 'Use sub products',
            default: false,
            index: true
        },
        hasAlternate: {
            type: 'boolean',
            label: 'Has alternates',
            default: false,
            index: true
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },

        // Barcodes
        barcodes: {
            type: [
                {
                    unitId: {
                        type: 'string',
                        label: 'Unit',
                        index: true
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        index: true
                    },
                    vendorId: {
                        type: 'string',
                        label: 'Vendor',
                        required: false
                    },
                    supplierCatalogNo: {
                        type: 'string',
                        label: 'Supplier catalog no',
                        required: false
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },
                    isDefault: {
                        type: 'boolean',
                        label: 'Is default',
                        default: false
                    }
                }
            ],
            default: []
        },

        // Units
        unitConversions: {
            type: [
                {
                    fromQty: {
                        type: 'decimal',
                        label: 'Quantity'
                    },
                    fromUnitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    toQty: {
                        type: 'decimal',
                        label: 'Corresponding quantity'
                    },
                    toUnitId: {
                        type: 'string',
                        label: 'Corresponding unit'
                    }
                }
            ],
            default: []
        },
        unitRatios: {
            type: 'object',
            required: false,
            blackbox: true
        },
        unitMeasurements: {
            type: [
                {
                    unitId: {
                        type: 'string',
                        label: 'Unit',
                        required: false
                    },
                    netWeight: {
                        type: 'decimal',
                        label: 'Net weight',
                        required: false
                    },
                    netWeightUnitId: {
                        type: 'string',
                        label: 'Net weight unit',
                        required: false
                    },
                    grossWeight: {
                        type: 'decimal',
                        label: 'Gross weight',
                        required: false
                    },
                    grossWeightUnitId: {
                        type: 'string',
                        label: 'Gross weight unit',
                        required: false
                    },
                    netVolume: {
                        type: 'decimal',
                        label: 'Net volume',
                        required: false
                    },
                    netVolumeUnitId: {
                        type: 'string',
                        label: 'Net volume unit',
                        required: false
                    },
                    grossVolume: {
                        type: 'decimal',
                        label: 'Gross volume',
                        required: false
                    },
                    grossVolumeUnitId: {
                        type: 'string',
                        label: 'Gross volume unit',
                        required: false
                    },
                    height: {
                        type: 'decimal',
                        label: 'Height',
                        required: false
                    },
                    heightUnitId: {
                        type: 'string',
                        label: 'Height unit',
                        required: false
                    },
                    width: {
                        type: 'decimal',
                        label: 'Width',
                        required: false
                    },
                    widthUnitId: {
                        type: 'string',
                        label: 'Width unit',
                        required: false
                    },
                    depth: {
                        type: 'decimal',
                        label: 'Depth',
                        required: false
                    },
                    depthUnitId: {
                        type: 'string',
                        label: 'Depth unit',
                        required: false
                    },
                    volumetricWeight: {
                        type: 'decimal',
                        label: 'Volumetric weight',
                        required: false
                    }
                }
            ],
            default: []
        },

        // Features
        features: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Variations
        variations: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Sale
        salesPrice: {
            type: 'decimal',
            label: 'Sales price',
            default: 0,
            index: true
        },
        salesUnitId: {
            type: 'string',
            label: 'Sales unit'
        },
        salesTaxId: {
            type: 'string',
            label: 'Sales tax'
        },
        salesAdditionalTaxIds: {
            type: ['string'],
            label: 'Additional taxes',
            default: []
        },
        salesNote: {
            type: 'string',
            label: 'Sales note',
            required: false
        },
        priceIsDeterminedBySubProducts: {
            type: 'boolean',
            label: 'Price is determined by sub-products',
            required: false
        },

        // Ecommerce.
        ecommerceSeoTitle: {
            type: 'string',
            label: 'SEO title',
            required: false
        },
        ecommerceSeoDescription: {
            type: 'string',
            label: 'SEO description',
            required: false
        },
        ecommerceTitle: {
            type: 'string',
            label: 'E-Commerce title',
            required: false
        },
        googleXmlFeedCategory: {
            type: 'string',
            label: 'Google xml feed category',
            required: false
        },
        ecommerceShowVariantProductsInTheCatalog: {
            type: 'boolean',
            label: 'Show variant products in the catalog',
            required: false
        },
        ecommerceDropshipping: {
            type: 'boolean',
            label: 'Dropshipping',
            default: false
        },
        ecommerceDeliveryOptionIds: {
            type: ['string'],
            label: 'Delivery options',
            default: []
        },
        ecommerceSameDayDelivery: {
            type: 'boolean',
            label: 'Same day delivery',
            default: false
        },
        ecommerceEstimatedDeliveryDuration: {
            type: 'decimal',
            label: 'Estimated delivery duration',
            required: false
        },
        ecommerceDeliveryAtSpecifiedDate: {
            type: 'boolean',
            label: 'Delivery at a specified date',
            required: false
        },
        ecommerceDeliveryAtSpecifiedTime: {
            type: 'boolean',
            label: 'Delivery at a specified time',
            required: false
        },
        ecommerceDailyStorageCost: {
            type: 'decimal',
            label: 'Daily storage cost',
            default: 0
        },

        // Purchase
        purchaseUnitId: {
            type: 'string',
            label: 'Purchase unit'
        },
        purchaseTaxId: {
            type: 'string',
            label: 'Purchase tax'
        },
        purchaseAdditionalTaxIds: {
            type: ['string'],
            label: 'Additional taxes',
            default: []
        },
        purchaseNote: {
            type: 'string',
            label: 'Purchase note',
            required: false
        },
        lastPurchasePrice: {
            type: 'decimal',
            label: 'Last purchase price',
            default: 0,
            index: true
        },

        // Inventory
        stockUnitId: {
            type: 'string',
            label: 'Stock unit'
        },
        tracking: {
            type: 'string',
            label: 'Tracking',
            allowed: ['serial', 'lot', 'none'],
            default: 'none',
            index: true
        },
        defaultSerialNumberTemplateId: {
            type: 'string',
            label: 'Default serial number template',
            required: false
        },
        defaultLotNumberTemplateId: {
            type: 'string',
            label: 'Default lot number template',
            required: false
        },
        putawayStrategy: {
            type: 'string',
            label: 'Putaway strategy',
            default: 'manuel'
        },
        removalStrategy: {
            type: 'string',
            label: 'Removal strategy',
            default: 'manuel'
        },
        negativeStock: {
            type: 'boolean',
            label: 'Allow negative stock',
            required: false
        },
        warehouseManagement: {
            type: [
                {
                    warehouseId: {
                        type: 'string',
                        label: 'Warehouse'
                    },
                    defaultPutawayLocationId: {
                        type: 'string',
                        label: 'Default putaway location',
                        required: false
                    },
                    isBlocked: {
                        type: 'boolean',
                        label: 'Is blocked',
                        default: false
                    }
                }
            ],
            default: []
        },
        lastCost: {
            type: 'decimal',
            default: 0
        },
        lastWarehouseCosts: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Service
        warrantyTemplateId: {
            type: 'string',
            label: 'Warranty template',
            required: false
        },
        serviceNote: {
            type: 'string',
            label: 'Service note',
            required: false
        },

        // Accounting
        cost: {
            type: 'decimal',
            label: 'Cost',
            default: 0,
            index: true
        },
        valuationMethod: {
            type: 'string',
            label: 'Valuation method',
            allowed: ['standard', 'average', 'fifo'],
            required: false,
            index: true
        },
        accountSetGroupId: {
            type: 'string',
            label: 'Account set group',
            required: false,
            index: true
        },
        useShippedGoodsAccount: {
            type: 'boolean',
            label: 'Use shipped goods account',
            default: false
        },

        // Fixture.
        fixtureAssetTypeId: {
            type: 'string',
            label: 'Asset type',
            required: false
        },
        fixtureAssetCategoryId: {
            type: 'string',
            label: 'Asset category',
            required: false
        },
        fixtureAssetClassId: {
            type: 'string',
            label: 'Asset class',
            required: false
        },
        fixtureModelId: {
            type: 'string',
            label: 'Model',
            required: false
        },

        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Content.
        content: {
            type: 'string',
            label: 'Content',
            required: false
        },

        // Images.
        images: {
            type: ['string'],
            default: []
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal
        attributeSetId: {
            type: 'string',
            label: 'Attribute set',
            required: false
        },
        configurableProductId: {
            type: 'string',
            label: 'Configurable product',
            required: false,
            index: true
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        },
        variantIds: {
            type: ['string'],
            default: []
        },
        variantImages: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        subProducts: {
            type: [
                {
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    type: {
                        type: 'string',
                        label: 'Type'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    canBeSold: {
                        type: 'boolean',
                        label: 'Can be sold',
                        default: true
                    },
                    canBePurchased: {
                        type: 'boolean',
                        label: 'Can be purchased',
                        default: true
                    },
                    canBeExpensed: {
                        type: 'boolean',
                        label: 'Can be expensed',
                        default: false
                    },
                    currencyId: {
                        type: 'string',
                        label: 'Currency',
                        required: false
                    },
                    price: {
                        type: 'decimal',
                        label: 'Price',
                        default: 0,
                        required: false
                    },
                    lastCost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0,
                        required: false
                    },
                    categoryId: {
                        type: 'string',
                        label: 'Category',
                        required: false
                    },
                    brandId: {
                        type: 'string',
                        label: 'Brand',
                        required: false
                    },
                    subProductAlternates: {
                        type: [
                            {
                                productId: {
                                    type: 'string',
                                    label: 'Product'
                                },
                                type: {
                                    type: 'string',
                                    label: 'Type'
                                },
                                quantity: {
                                    type: 'decimal',
                                    label: 'Quantity',
                                    default: 1
                                },
                                unitId: {
                                    type: 'string',
                                    label: 'Unit'
                                },
                                canBeSold: {
                                    type: 'boolean',
                                    label: 'Can be sold',
                                    default: true
                                },
                                canBePurchased: {
                                    type: 'boolean',
                                    label: 'Can be purchased',
                                    default: true
                                },
                                canBeExpensed: {
                                    type: 'boolean',
                                    label: 'Can be expensed',
                                    default: false
                                },
                                currencyId: {
                                    type: 'string',
                                    label: 'Currency',
                                    required: false
                                },
                                price: {
                                    type: 'decimal',
                                    label: 'Price',
                                    default: 0,
                                    required: false
                                },
                                lastCost: {
                                    type: 'decimal',
                                    label: 'Cost',
                                    default: 0,
                                    required: false
                                },
                                categoryId: {
                                    type: 'string',
                                    label: 'Category',
                                    required: false
                                },
                                brandId: {
                                    type: 'string',
                                    label: 'Brand',
                                    required: false
                                }
                            }
                        ],
                        default: []
                    }
                }
            ],
            default: []
        },
        alternateProducts: {
            type: [
                {
                    productId: {
                        type: 'string',
                        label: 'Product'
                    },
                    type: {
                        type: 'string',
                        label: 'Type'
                    },
                    canBeSold: {
                        type: 'boolean',
                        label: 'Can be sold',
                        default: true
                    },
                    canBePurchased: {
                        type: 'boolean',
                        label: 'Can be purchased',
                        default: true
                    },
                    canBeExpensed: {
                        type: 'boolean',
                        label: 'Can be expensed',
                        default: false
                    }
                }
            ],
            default: []
        },
        attributes: {
            type: 'object',
            blackbox: true,
            required: false
        },
        params: {
            type: 'object',
            blackbox: true,
            required: false
        },
        externalId: {
            type: 'string',
            label: 'External id',
            required: false,
            index: true
        }
        ,
        expenseCategoryId: {
            type: 'string',
            label: 'Expense category',
            required: false,
            index: true
        },
        expenseTagIds: {
            type: ['string'],
            label: 'Expense tags',
            default: []
        }
    },
    attributes: {
        groups: {
            collection: 'inventory.product-groups',
            parentField: 'groupIds',
            childField: '_id'
        },
        category: {
            collection: 'inventory.product-categories',
            parentField: 'categoryId',
            childField: '_id'
        },
        brand: {
            collection: 'inventory.product-brands',
            parentField: 'brandId',
            childField: '_id'
        },
        manufacturer: {
            collection: 'kernel.partners',
            parentField: 'manufacturerId',
            childField: '_id'
        },
        baseUnit: {
            collection: 'kernel.units',
            parentField: 'baseUnitId',
            childField: '_id'
        },
        specialUnit: {
            collection: 'kernel.units',
            parentField: 'specialUnitId',
            childField: '_id'
        },
        attributeSet: {
            collection: 'inventory.product-attribute-sets',
            parentField: 'attributeSetId',
            childField: '_id'
        },
        expenseCategory: {
            collection: 'expense.expense-categories',
            parentField: 'expenseCategoryId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['displayName', 'barcode', 'manufacturerProductCode']));

        if (_.isPlainObject(document.brand)) {
            values.push(document.brand.name);
        }
        if (Array.isArray(document.barcodes) && document.barcodes.length > 0) {
            for (const item of document.barcodes) {
                values.push(item.barcode || '');
            }
        }

        if (_.isPlainObject(document.features)) {
            for (const value of Object.values(document.features)) {
                values.push(value);
            }
        }

        if (_.isPlainObject(document.additionalInformation)) {
            for (const value of Object.values(document.additionalInformation)) {
                values.push(value);
            }
        }

        return values;
    },
    hooks: {
        before: {
            create: [fixDefaultNegativeStock, shouldUpdateDisplayNameAndDefinition, setCategoryPath, fixIsSimple],
            update: [shouldUpdateDisplayNameAndDefinition, setCategoryPath, fixIsSimple],
            patch: [shouldUpdateDisplayNameAndDefinition, setCategoryPath, fixIsSimple]
        },
        after: {
            create: [updateDisplayNameAndDefinition, adjustKitSubProducts],
            update: [updateDisplayNameAndDefinition, adjustKitSubProducts],
            patch: [updateDisplayNameAndDefinition, adjustKitSubProducts],
            remove: [fixRemovedVariantIds]
        }
    }
};

function fixDefaultNegativeStock(context) {
    if (Array.isArray(context.data)) {
        context.data = context.data.map(item => {
            if (_.isUndefined(item.negativeStock) || _.isUndefined(item.negativeStock)) {
                item.negativeStock = context.app.setting('inventory.negativeStock');
            }

            return item;
        });
    } else {
        if (_.isUndefined(context.data.negativeStock) || _.isUndefined(context.data.negativeStock)) {
            context.data.negativeStock = context.app.setting('inventory.negativeStock');
        }
    }

    return context;
}

function shouldUpdateDisplayNameAndDefinition(context) {
    const data = (context.method === 'create' ? context.data : context.data.$set) || {};

    context.params.shouldUpdatProducteDisplayName =
        data.code || data.name || data.attributes || Object.keys(data).some(k => k.indexOf('attributes.') !== -1);

    return context;
}

async function updateDisplayNameAndDefinition(context) {
    if (context.params.shouldUpdatProducteDisplayName) {
        delete context.params.shouldUpdatProducteDisplayName;

        const result = Array.isArray(context.result) ? context.result : [context.result];

        if (result.length < 1) return context;

        const operations = [];

        for (const product of result) {
            let attributeSet = null;
            let parts = [];

            if (!!product.attributeSetId && _.isPlainObject(product.attributes) && !_.isEmpty(product.attributes)) {
                attributeSet = await context.app.collection('inventory.product-attribute-sets').findOne({
                    _id: product.attributeSetId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const positionMappedAttributes = _.groupBy(attributeSet.attributes, 'position');

                for (const attribute of positionMappedAttributes['before'] || []) {
                    const value = product.attributes[attribute.code];

                    if (!!value) {
                        parts.push(value);
                    }
                }

                parts.push(product.name);

                for (const attribute of positionMappedAttributes['after'] || []) {
                    const value = product.attributes[attribute.code];

                    if (!!value) {
                        parts.push(value);
                    }
                }
            } else {
                parts.push(product.name);
            }

            let separator = ',';
            if (_.isPlainObject(attributeSet) && !!_.isString(attributeSet.separator)) {
                separator = attributeSet.separator;
            }

            product.displayName = `${product.code} - ${parts.map(p => p.trim()).join(separator)}`;
            product.definition = parts.map(p => p.trim()).join(separator);

            operations.push({
                updateOne: {
                    filter: {_id: new ObjectId(product._id)},
                    update: {
                        $set: {
                            displayName: product.displayName,
                            definition: product.definition
                        }
                    }
                }
            });
        }

        await context.app.collection('inventory.products').bulkWrite(operations);
    }

    return context;
}

async function setCategoryPath(context) {
    if (context.method === 'create') {
        if (context.data.categoryId) {
            const categoryId = context.data.categoryId;
            const category = await context.app.collection('inventory.product-categories').findOne({
                _id: categoryId,
                $select: ['path']
            });

            if (_.isObject(category)) {
                context.data.categoryPath = category.path;
            }
        }
    } else if (_.isObject(context.data.$set) && context.data.$set.categoryId) {
        const categoryId = context.data.$set.categoryId;
        const category = await context.app.collection('inventory.product-categories').findOne({
            _id: categoryId,
            $select: ['path']
        });

        if (_.isObject(category)) {
            context.data.$set.categoryPath = category.path;
        }
    }

    return context;
}

function fixIsSimple(context) {
    if (context.method === 'create') {
        context.data.isSimple = !context.data.isConfigurable;

        if (context.data.isSimple) {
            context.data.isSimple = !context.data.isKit;
        }

        if (_.isObject(context.data.attributes) && !_.isEmpty(context.data.attributes)) {
            context.data.isVariant = true;
        }
    } else if (_.isObject(context.data.$set)) {
        if (context.data.$set.isConfigurable) {
            context.data.$set.isSimple = false;
        }

        if (context.data.$set.isKit) {
            context.data.$set.isSimple = false;
        }
    }

    return context;
}

function adjustKitSubProducts(context) {
    const app = context.app;
    const result = Array.isArray(context.result) ? context.result : [context.result];

    if (result.length < 1) return context;

    (async () => {
        const operations = [];

        for (const product of result) {
            if (!!product.isKit && Array.isArray(product.subProducts) && product.subProducts.length > 0) {
                const subProducts = await app.collection('inventory.products').find({
                    _id: {$in: product.subProducts.map(sp => sp.productId)},
                    $select: ['_id', 'canBeSold', 'canBePurchased', 'canBeExpensed'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                product.subProducts = product.subProducts.map(sp => {
                    const subProduct = subProducts.find(p => p._id === sp.productId);

                    sp.canBeSold = !!subProduct.canBeSold;
                    sp.canBePurchased = !!subProduct.canBePurchased;
                    sp.canBeExpensed = !!subProduct.canBeExpensed;

                    return sp;
                });

                operations.push({
                    updateOne: {
                        filter: {_id: new ObjectId(product._id)},
                        update: {$set: {subProducts: product.subProducts}}
                    }
                });
            } else {
                const kitProducts = await app.collection('inventory.products').find({
                    'subProduct.productId': product._id,
                    $select: ['_id', 'subProducts'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                for (const kitProduct of kitProducts) {
                    const subProducts = kitProduct.subProducts.map(sp => {
                        if (sp.productId === product._id) {
                            sp.canBeSold = !!product.canBeSold;
                            sp.canBePurchased = !!product.canBePurchased;
                            sp.canBeExpensed = !!product.canBeExpensed;
                        }

                        return sp;
                    });

                    operations.push({
                        updateOne: {
                            filter: {_id: new ObjectId(kitProduct._id)},
                            update: {$set: {subProducts}}
                        }
                    });
                }
            }
        }

        if (operations.length > 0) {
            await app.collection('inventory.products').bulkWrite(operations);
        }
    })();

    return context;
}

function fixRemovedVariantIds(context) {
    const app = context.app;
    let result = Array.isArray(context.result) ? context.result : [context.result];

    result = result.filter(product => product.isVariant);

    if (result.length < 1) return context;

    (async () => {
        const operations = [];

        for (const product of result) {
            const configurableProduct = await app.collection('inventory.products').findOne({
                isConfigurable: true,
                variantIds: product._id,
                $select: ['variantIds'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (_.isPlainObject(configurableProduct)) {
                const variantIds = configurableProduct.variantIds.filter(id => id !== product._id);

                operations.push({
                    updateOne: {
                        filter: {_id: new ObjectId(configurableProduct._id)},
                        update: {$set: {variantIds}}
                    }
                });
            }
        }

        if (operations.length > 0) {
            await app.collection('inventory.products').bulkWrite(operations);
        }
    })();

    return context;
}
