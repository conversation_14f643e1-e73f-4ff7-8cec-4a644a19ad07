<template>
    <el-tabs v-model="activeTab" class="full-tabs">
        <el-tab-pane name="general" :label="'General'|t">
            <el-scrollbar>
                <div class="columns">
                    <div class="column is-half">
                        <ui-legend title="Payments"/>
                        <ui-field name="paymentTermId"
                                  collection="finance.payment-terms"
                                  view="finance.configuration.payment-terms"
                                  disable-create
                                  disable-detail
                                  :filters="paymentTermIdFilters"
                                  :extra-fields="['code']"
                                  :template="'{{code}} - {{name}}'"/>
                    </div>
                    <div class="column is-half">
                        <ui-legend title="Endorsable"/>
                        <ui-field name="acceptEndorsedCheques"
                                  label="hide"
                                  :active-text="'This partner accepts endorsed cheques'|t" class="mb0"/>
                        <ui-field name="chequesEndorsable"
                                  label="hide"
                                  :active-text="'This partner cheques can be endorsed'|t" class="mb0"/>
                        <ui-field name="acceptEndorsedPromissoryNotes"
                                  label="hide"
                                  :active-text="'This partner accepts endorsed promissory notes'|t" class="mb0"/>
                        <ui-field name="promissoryNotesEndorsable"
                                  label="hide"
                                  :active-text="'This partner promissory notes can be endorsed'|t" class="mb0"/>
                    </div>
                </div>
            </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane name="limits" :label="'Limits'|t" :disabled="isEmployee">
            <el-scrollbar>
                <div class="columns">
                    <div class="column is-half">
                        <ui-legend title="General"/>
                        <ui-field name="enableLimitChecks"/>
                        <ui-field name="limitControlDocument"
                                  :options="limitControlDocumentOptions"
                                  translate-labels
                                  :disabled="!model.enableLimitChecks"/>
                        <ui-field name="limitOrderControl"
                                  :options="limitControlOptions"
                                  translate-labels
                                  :disabled="!model.enableLimitChecks"
                                  v-show="model.limitControlDocument === 'order' || model.limitControlDocument === 'quotation'"/>
                        <ui-field name="limitInvoiceControl"
                                  :options="limitControlOptions"
                                  translate-labels
                                  :disabled="!model.enableLimitChecks"/>
                        <ui-field name="blacklist"/>
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Details"/>
                        <ui-field name="openAccountLimit" :precision="$setting('system.currencyPrecision')">
                            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                                {{ currencyFormat.currency.symbol }}
                            </div>
                        </ui-field>
                        <ui-field name="guaranteeLimit" :precision="$setting('system.currencyPrecision')" disabled>
                            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                                {{ currencyFormat.currency.symbol }}
                            </div>
                        </ui-field>
                        <ui-field name="accountLimit" :precision="$setting('system.currencyPrecision')" disabled>
                            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                                {{ currencyFormat.currency.symbol }}
                            </div>
                        </ui-field>
                        <ui-field name="riskLimit" :precision="$setting('system.currencyPrecision')" disabled>
                            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                                {{ currencyFormat.currency.symbol }}
                            </div>
                        </ui-field>
                        <ui-field name="totalLimit" :precision="$setting('system.currencyPrecision')" disabled>
                            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                                {{ currencyFormat.currency.symbol }}
                            </div>
                        </ui-field>
                    </div>
                </div>

                <ui-legend title="Guarantees" class="full-width mt20 mb0" v-show="!!$params('id')"/>
                <ui-field name="guarantees"
                          :key="model.type"
                          :schema="guaranteesSchema"
                          :enable-enlarge="true"
                          :min-empty-rows="3"
                          :before-init="beforeGuaranteesInit"
                          :before-create="beforeSaveGuarantee"
                          :before-update="beforeSaveGuarantee"
                          v-show="!!$params('id')"/>
            </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane name="credit-cards" :label="'Credit Cards'|t" :disabled="isEmployee">
            <ui-field name="creditCardIds"
                      field-type="relation"
                      collection="kernel.partner-credit-cards"
                      actions="create,delete"
                      view="partners.partners.tab-finance.credit-cards"
                      :columns="creditCardColumns"
                      :extra-fields="['expireMonth', 'expireYear']"

            />
        </el-tab-pane>

        <el-tab-pane name="bank-accounts" :label="'Bank Accounts'|t">
            <ui-field name="bankAccountIds"
                      field-type="relation"
                      collection="kernel.partner-bank-accounts"
                      actions="create,delete"
                      view="partners.partners.tab-finance.bank-accounts"
                      :columns="bankAccountColumns"
                      :extra-fields="['bankBranch', 'bank']"
            />
        </el-tab-pane>
    </el-tabs>
</template>

<script>
import _ from 'lodash';
import {leadingZeros} from 'framework/helpers';

export default {
    props: {
        model: Object,
        isCustomer: Boolean,
        isVendor: Boolean,
        isEmployee: Boolean,
        currencyFormat: Object,
        organizationSettings: Object
    },

    data: () => ({
        activeTab: 'general',
        limitControlDocumentOptions: [
            {value: 'order', label: 'Order'},
            {value: 'quotation', label: 'Quotation'},
            {value: 'invoice', label: 'Invoice'}
        ],
        limitControlOptions: [
            {value: 'none', label: 'None'},
            {value: 'warn', label: 'Warn'},
            {value: 'block', label: 'Block'}
        ],
        creditCardColumns: [
            {field: 'cardHolder', label: 'Card Holder'},
            {field: 'cardNumber', label: 'Card Number'},
            {
                field: 'expiry',
                label: 'Expiration Date',
                render(params) {
                    if (_.isObject(params.data) && params.data.expireMonth && params.data.expireYear) {
                        return `${leadingZeros(params.data.expireMonth, 2)}/${params.data.expireYear}`;
                    }

                    return '';
                }
            },
            {
                field: 'isDefault',
                label: 'Is default',
                type: 'boolean',
                width: 120
            }
        ],
        bankAccountColumns: [
            {
                field: 'name',
                label: 'Name'
            },
            {
                field: 'bank.name',
                label: 'Bank'
            },
            {
                field: 'bankBranch',
                label: 'Bank Branch Office',
                render(params) {
                    if (_.isObject(params.data) && _.isObject(params.data.bankBranch)) {
                        return `${params.data.bankBranch.code} - ${params.data.bankBranch.name}`;
                    }

                    return '';
                }
            },
            {
                field: 'accountNumber',
                label: 'Account Number'
            },
            {
                field: 'bankBranch.phone',
                label: 'Phone'
            },
            {
                field: 'bankBranch.email',
                label: 'Email Address'
            },
            {
                field: 'isDefault',
                label: 'Is default',
                type: 'boolean',
                width: 120
            }
        ]
    }),

    computed: {
        paymentTermIdFilters() {
            const filters = {};

            if (_.isPlainObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {$in: this.organizationSettings.paymentTermIds || []};
            }

            return filters;
        },
        guaranteesSchema() {
            const self = this;

            return {
                guaranteeId: {
                    type: 'string',
                    label: 'Guarantee',
                    column: {
                        populate: 'guarantee',
                        minWidth: 180
                    },
                    editor: {
                        collection: 'finance.guarantees',
                        view: 'finance.banking.guarantees.list',
                        disableCreate: true,
                        disableDetail: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}',
                        filters: () => ({
                            ...(!!this.model.guarantees && this.model.guarantees.length > 0 ? {_id: {$nin: this.model.guarantees.map(g => g.guaranteeId)}} : {}),
                            ...(this.model.type === 'customer' ? {type: 'partner-received'} : {type: 'partner-issued'}),
                            // currencyId: this.model.currencyId,
                            partnerId: this.$params('id'),
                            status: 'approved'
                        })
                    }
                },
                guaranteeMethod: {
                    type: 'string',
                    label: 'Guarantee method',
                    required: false,
                    column: {
                        width: 120
                    },
                    editor: {
                        disabled: true,
                        translateLabels: true,
                        options: [
                            {value: 'none', label: 'None'},
                            {value: 'cash', label: 'Cash'},
                            {value: 'cheque', label: 'Cheque'},
                            {value: 'promissoryNote', label: 'Promissory note'},
                            {value: 'mortgage', label: 'Mortgage'},
                            {value: 'letter-of-guarantee', label: 'Letter of guarantee'},
                            {value: 'dbs', label: 'DBS'}
                        ]
                    }
                },
                accountLimit: {
                    type: 'decimal',
                    label: 'Account limit',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions(row) {
                            return self.currencyFormat;
                        },
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },
                riskLimit: {
                    type: 'decimal',
                    label: 'Risk limit (-/+)',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions(row) {
                            return self.currencyFormat;
                        },
                        width: 150
                    },
                    editor: {}
                },
                totalLimit: {
                    type: 'decimal',
                    label: 'Total limit',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions(row) {
                            return self.currencyFormat;
                        },
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },
                startDate: {
                    type: 'date',
                    label: 'Start date',
                    required: false,
                    column: {
                        width: 120
                    }
                },
                endDate: {
                    type: 'date',
                    label: 'End date',
                    required: false,
                    column: {
                        width: 120
                    }
                },
                isActive: {
                    type: 'boolean',
                    label: 'Is active',
                    default: true,
                    column: {
                        width: 75
                    }
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Project',
                    required: false,
                    column: {
                        populate: 'financialProject',
                        width: 150
                    },
                    editor: {
                        collection: 'kernel.financial-projects',
                        view: 'system.management.configuration.financial-projects',
                        disableCreate: true,
                        disableDetail: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}',
                        filters: () => ({
                            $and: [
                                {
                                    $or: [
                                        {validFrom: {$exists: false}},
                                        {validFrom: {$eq: null}},
                                        {validFrom: {$lte: new Date()}}
                                    ]
                                },
                                {
                                    $or: [
                                        {validTo: {$exists: false}},
                                        {validTo: {$eq: null}},
                                        {validTo: {$gte: new Date()}}
                                    ]
                                }
                            ],
                            $sort: {code: 1}
                        })
                    }
                }
            };
        }
    },

    methods: {
        async beforeGuaranteesInit(items) {
            let guaranteeIds = [];
            let guarantees = [];
            items.forEach(item => {
                if (item.guaranteeId) guaranteeIds.push(item.guaranteeId);
            });
            guaranteeIds = _.uniq(guaranteeIds);
            if (guaranteeIds.length > 0) {
                guarantees = await this.$collection('finance.guarantees').find({
                    _id: {$in: guaranteeIds},
                    $select: ['name', 'code'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return items.map(item => {
                if (item.guaranteeId) item.guarantee = guarantees.find(guarantee => guarantee._id === item.guaranteeId);

                return item;
            });
        },
        async beforeSaveGuarantee({row, params}) {
            const field = params.colDef.field;

            if (_.isString(row.guaranteeId)) {
                row.guarantee = await this.$collection('finance.guarantees').findOne({
                    _id: row.guaranteeId,
                    $select: ['name', 'code', 'currencyId', 'guaranteeMethod', 'startDate', 'endDate', 'amount'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (field === 'guaranteeId') {
                    row.guaranteeMethod = row.guarantee.guaranteeMethod;
                    row.accountLimit = row.guarantee.amount;
                    row.startDate = row.guarantee.startDate;
                    row.endDate = row.guarantee.endDate;

                    if (this.model.currencyId !== row.guarantee.currencyId) {
                        const fromCurrency = await this.$collection('kernel.currencies').findOne({
                            _id: row.guarantee.currencyId,
                            $select: ['name']
                        });
                        const toCurrency = await this.$collection('kernel.currencies').findOne({
                            _id: this.model.currencyId,
                            $select: ['name']
                        });
                        const currencyRate = await this.$convertCurrency({
                            from: fromCurrency.name,
                            to: toCurrency.name,
                            value: 1,
                            options: {
                                date: new Date()
                            }
                        });

                        row.accountLimit =  this.$app.round(row.accountLimit * currencyRate, 'currency');
                    }
                }
            }

            row.totalLimit = row.accountLimit + row.riskLimit;

            return row;
        }
    }
};
</script>
