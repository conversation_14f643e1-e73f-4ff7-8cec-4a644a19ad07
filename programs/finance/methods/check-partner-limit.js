import _ from 'lodash';

export default {
    name: 'check-partner-limit',
    async action(payload, params) {
        const app = this.app;

        if (!payload.partnerId) return null;

        const partner = await app.collection('kernel.partners').findOne({
            _id: payload.partnerId,
            $select: [
                'currencyId',
                'enableLimitChecks',
                'limitControlDocument',
                'limitOrderControl',
                'limitInvoiceControl',
                'blacklist',
                'openAccountLimit',
                'guarantees'
            ],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });

        if (!!partner.blacklist) return {blacklist: true};

        if (!partner.enableLimitChecks) return null;

        let additionalLimit = 0;
        if (!!payload.linkedDocumentIds && payload.linkedDocumentIds.length > 0) {
            const limits = await app.collection('finance.partner-limit-transactions').find({
                documentId: {$in: payload.linkedDocumentIds},
                $select: ['amount']
            });

            for (const limit of limits) {
                additionalLimit += Math.abs(limit.amount);
            }
        }

        const documentCurrency = await app.collection('kernel.currencies').findOne({
            _id: payload.currencyId,
            $select: ['name', 'symbolPosition', 'symbol'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const partnerCurrency = await app.collection('kernel.currencies').findOne({
            _id: partner.currencyId,
            $select: ['name', 'symbolPosition', 'symbol'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const currencyFormat = {
            currency: {
                symbol: partnerCurrency.symbol,
                symbolPosition: partnerCurrency.symbolPosition,
                format: partnerCurrency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                // decimal: decimalSeparator,
                // thousand: thousandsSeparator
            }
        };
        const guarantee = !!payload.guaranteeId
            ? (partner.guarantees || []).find(g => g.guaranteeId === payload.guaranteeId)
            : null;
        const usedReport = await app.collection('finance.partner-limit-transactions').aggregate([
            {
                $match: {
                    partnerId: payload.partnerId,
                    ...(!!guarantee
                        ? {guaranteeId: payload.guaranteeId}
                        : {
                              $or: [{guaranteeId: null}, {guaranteeId: ''}, {guaranteeId: {$exists: false}}]
                          })
                }
            },
            {
                $group: {
                    _id: null,
                    amount: {$sum: '$amount'}
                }
            }
        ]);
        const used = Math.abs(usedReport.length > 0 ? usedReport[0].amount : 0);
        let total = partner.openAccountLimit;
        if (!!guarantee) {
            total = guarantee.totalLimit || 0;
        }
        total += additionalLimit;
        let amount = payload.amount;
        if (payload.currencyId !== partner.currencyId) {
            amount = await app.rpc('kernel.common.convert-currency', {
                from: documentCurrency.name,
                to: partnerCurrency.name,
                value: payload.amount,
                options: {
                    date: app.datetime.local().toJSDate()
                }
            });
        }
        let reaction = (payload.document === 'order' || payload.document === 'quotation') ? partner.limitOrderControl : partner.limitInvoiceControl;
        if (payload.document === 'return-invoice') {
            amount = -amount;
            reaction = false;
        }
        const hasLimit = total - used - amount > 0;
        const result = {
            total,
            used,
            usable: total - used,
            amount,
            hasLimit,
            reaction,
            currencyFormat
        };
        let amountInCurrency = result.usable - result.amount;

        if (partner.currencyId !== payload.currencyId) {
            amountInCurrency = await app.rpc('kernel.common.convert-currency', {
                from: partnerCurrency.name,
                to: documentCurrency.name,
                value: amountInCurrency,
                options: {
                    date: app.datetime.local().toJSDate()
                }
            });
        }
        amountInCurrency = app.format(amountInCurrency, 'currency', currencyFormat);

        if (!result.hasLimit) {
            if (result.reaction === 'warn') {
                result.message = app.translate(
                    'You have exceeded the maximum limit defined for the partner. Do you want to continue? Limit excess amount is {{amount}}.',
                    {amount: amountInCurrency}
                );
            } else if (result.reaction === 'block') {
                result.message = app.translate(
                    'You have exceeded the maximum limit defined for the partner. The transaction is blocked. Limit excess amount is {{amount}}.',
                    {amount: amountInCurrency}
                );
            }

            result.statusMessage = app.translate(
                'You have exceeded the maximum limit defined for the partner. Limit excess amount is {{amount}}.',
                {amount: amountInCurrency}
            );
        }

        return result;
    }
};
