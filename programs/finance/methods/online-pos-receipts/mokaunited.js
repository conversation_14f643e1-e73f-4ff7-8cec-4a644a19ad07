import _ from 'lodash';
import microtime from 'microtime';
import axios from 'axios';
import {secureCardNumber, trim} from 'framework/helpers';

const charge = async (app, payload, params) => {
    const company = await app.collection('kernel.company').findOne({});

    const {dealerCode, username, password, merchantKey} = JSON.parse(payload.pos.integrationParams);

    const merchant_oid = 'OPR' + microtime.now();
    const user_ip = typeof payload.ip === 'string' && payload.ip.length > 0 ? payload.ip : '127.0.0.1';
    const email = payload.partner.email || company.email;
    const payment_amount = app.roundNumber(payload.amount, 2).toString();
    const currency = payload.currency.name;
    const user_name = payload.partner.name;
    const user_address = (payload.partner.address || {}).address || company.address.address;
    const user_phone = payload.partner.phone || company.phone;
    const client_lang = payload.locale || app.config('app.locale');
    const installment_count = (payload.installmentCount === 1 ? 0 : payload.installmentCount).toString();

    await app.collection('finance.online-pos-receipts').create(
        {
            status: 'waiting',
            code: merchant_oid,
            partnerId: payload.partnerId,
            storeId: payload.storeId,
            branchId: payload.branchId,
            date: app.datetime.local().toJSDate(),
            currencyId: payload.currencyId,
            currencyRate: payload.currencyRate || 1,
            amount: payload.amount,
            cardNumber: secureCardNumber(payload.cardNumber),
            cardHolder: payload.cardHolder,
            cardBrand: payload.cardBrand,
            installmentCount: payload.installmentCount,
            installmentAmount: app.roundNumber(payload.amount / payload.installmentCount, 2),
            posId: payload.posId,
            description: payload.description,
            referenceId: payload.referenceId,
            referenceCode: payload.referenceCode,
            referenceCollection: payload.referenceCollection,
            referenceView: payload.referenceView,
            entryPayload: payload.entryPayload,
            ip: payload.ip || '',
            cartItems: Array.isArray(payload.cartItems) ? payload.cartItems : [],
            ...(!!payload.cart ? {cart: payload.cart} : {}),
            integrationPayload: {
                dealerCode,
                username,
                merchantKey,
                user_ip,
                merchant_oid,
                email,
                payment_amount,
                currency,
                merchant_ok_url: !!payload.okUrl
                    ? `${trim(payload.okUrl, '/')}?code=${merchant_oid}`
                    : app.absoluteUrl(`api/finance/online-pos-receipt-mokaunited-success?code=${merchant_oid}`),
                merchant_fail_url: !!payload.failUrl
                    ? `${trim(payload.failUrl, '/')}?code=${merchant_oid}`
                    : app.absoluteUrl(`api/finance/online-pos-receipt-mokaunited-error?code=${merchant_oid}`),
                user_name,
                user_address,
                user_phone,
                client_lang,
                installment_count,
                card_type: payload.cardBrand || 'none',
                cc_owner: payload.cardHolder,
                card_number: payload.cardNumber,
                expiry_month: payload.expireMonth.toString(),
                expiry_year: payload.expireYear.toString(),
                cvv: payload.cvv
            }
        },
        {user: params.user}
    );

    const requestBody = {
        PaymentDealerAuthentication: {
            DealerCode: dealerCode,
            Username: username,
            Password: password,
            CheckKey: merchantKey
        },
        PaymentDealerRequest: {
            CardHolderFullName: payload.cardHolder,
            CardNumber: payload.cardNumber,
            ExpMonth: payload.expireMonth.toString().length < 2
                ? `0${payload.expireMonth.toString()}`
                : payload.expireMonth.toString(),
            ExpYear: payload.expireYear.toString(),
            CvcNumber: payload.cvv,
            Amount: app.roundNumber(payload.amount, 2),
            Currency: currency,
            InstallmentNumber: payload.installmentCount,
            ClientIP: user_ip,
            OtherTrxCode: merchant_oid,
            IsPoolPayment: 0,
            IsPreAuth: 0,
            IsTokenized: 0,
            Software: 'ENTERERP',
            Description: payload.description || '',
            ReturnHash: 1,
            RedirectUrl: app.absoluteUrl(`api/finance/online-pos-receipt-mokaunited-callback?code=${merchant_oid}`),
            RedirectType: 1,
            BuyerInformation: {
                BuyerFullName: user_name,
                BuyerEmail: email,
                BuyerGsmNumber: user_phone,
                BuyerAddress: user_address
            }
        }
    };

    let error = null;

    try {
        const response = await axios.post('https://service.mokaunited.com/PaymentDealer/DoDirectPaymentThreeD', requestBody, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            })
        });

        const result = response.data;

        if (result.ResultCode === 'Success' && result.Data) {
            await app.collection('finance.online-pos-receipts').patch(
                {code: merchant_oid},
                {
                    'integrationPayload.codeForHash': result.Data.CodeForHash,
                    'iFrame3dUrl': result.Data.Url
                }
            );
        } else {
            if (result.ResultCode === 'PaymentDealer.CheckPaymentDealerAuthentication.InvalidAccount') {
                error = app.translate('Invalid MokaPOS account credentials. Please check your DealerCode, Username, and Password.');
            } else if (result.ResultCode && result.ResultCode.includes('InvalidAccount')) {
                error = app.translate('Invalid MokaPOS account. Please check your account credentials.');
            } else if (result.ResultCode && result.ResultCode.includes('Authentication')) {
                error = app.translate('MokaPOS authentication error. Please check your account credentials.');
            } else {
                error = result.ResultMessage || app.translate('Unknown POS error!');
            }
        }
    } catch (err) {
        if (err.response && err.response.data) {
            error = err.response.data.ResultMessage || err.response.data.Message || err.message || app.translate('Unknown POS error!');
        } else {
            error = err.message || app.translate('Unknown POS error!');
        }
    }

    if (error) {
        await app.collection('finance.online-pos-receipts').patch(
            {code: merchant_oid},
            {
                status: 'errored',
                errorMessage: error
            }
        );

        throw new app.errors.Unprocessable(error);
    }

    return {
        status: 'redirect',
        code: merchant_oid,
        url: app.absoluteUrl(`api/finance/online-pos-receipt-mokaunited-frame-3d?code=${merchant_oid}`)
    };
};

const list = async (app, payload, params) => {
    const {dealerCode, username, password, merchantKey} = JSON.parse(payload.pos.integrationParams);
    console.log('payload',payload.onlinePOSReceiptId)
    const opr = await app.collection('finance.online-pos-receipts').findOne({
        _id: payload.onlinePOSReceiptId,
        $select: ['_id', 'code', 'amount', 'referenceCode'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

console.log('OPR Details:', {
    code: opr.code,
    referenceCode: opr.referenceCode, // Bu değer doğru mu?
    status: opr.status,
    integrationPayload: opr.integrationPayload
});
    const requestBody = {
        PaymentDealerAuthentication: {
            DealerCode: dealerCode,
            Username: username,
            Password: password,
            CheckKey: merchantKey
        },
        PaymentDealerRequest:{
      PaymentStartDate:"2025-05-01 14:00",
      PaymentEndDate:"2025-05-28 15:00",
      PaymentStatus:2,
      TrxStatus:1
   }
    };

    try {
        const response = await axios.post('https://service.mokaunited.com/PaymentDealer/GetPaymentList', requestBody, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            })
        });

        
        const result = response.data;

        console.log('result',result.Data.PaymentList)

        
        if (result.ResultCode !== 'Success') {
            let errorMessage;

            if (result.ResultCode === 'PaymentDealer.CheckPaymentDealerAuthentication.InvalidAccount') {
                errorMessage = app.translate('Invalid MokaPOS account credentials. Please check your DealerCode, Username, and Password.');
            } else if (result.ResultCode && result.ResultCode.includes('InvalidAccount')) {
                errorMessage = app.translate('Invalid MokaPOS account. Please check your account credentials.');
            } else if (result.ResultCode && result.ResultCode.includes('Authentication')) {
                errorMessage = app.translate('MokaPOS authentication error. Please check your account credentials.');
            } else {
                errorMessage = result.ResultMessage || app.translate('Unknown POS error!');
            }

            throw new app.errors.Unprocessable(errorMessage);
        }

        await app
            .collection('finance.online-pos-receipts')
            .patch({_id: payload.onlinePOSReceiptId}, {status: 'canceled'}, {user: params.user});
    } catch (err) {
        let errorMessage;
        if (err.response && err.response.data) {
            errorMessage = err.response.data.ResultMessage || err.response.data.Message || err.message || app.translate('Unknown POS error!');
        } else {
            errorMessage = err.message || app.translate('Unknown POS error!');
        }
        throw new app.errors.Unprocessable(errorMessage);
    }
};

const refund = async (app, payload, params) => {
    list(app,payload,params)
    const {dealerCode, username, password, merchantKey} = JSON.parse(payload.pos.integrationParams);
    const opr = await app.collection('finance.online-pos-receipts').findOne({
        _id: payload.onlinePOSReceiptId,
        $select: ['_id', 'code', 'amount', 'referenceCode'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    const requestBody = {
        PaymentDealerAuthentication: {
            DealerCode: dealerCode,
            Username: username,
            Password: password,
            CheckKey: merchantKey
        },
        PaymentDealerRequest: {
            VirtualPosOrderId: opr.referenceCode,
            OtherTrxCode: opr.code,
            Amount: app.roundNumber(payload.amount, 2)
        }
    };

    try {
        const response = await axios.post('https://service.mokaunited.com/PaymentDealer/DoCreateRefundRequest', requestBody, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            })
        });

        
        const result = response.data;

        
        if (result.ResultCode !== 'Success') {
            let errorMessage;

            if (result.ResultCode === 'PaymentDealer.CheckPaymentDealerAuthentication.InvalidAccount') {
                errorMessage = app.translate('Invalid MokaPOS account credentials. Please check your DealerCode, Username, and Password.');
            } else if (result.ResultCode && result.ResultCode.includes('InvalidAccount')) {
                errorMessage = app.translate('Invalid MokaPOS account. Please check your account credentials.');
            } else if (result.ResultCode && result.ResultCode.includes('Authentication')) {
                errorMessage = app.translate('MokaPOS authentication error. Please check your account credentials.');
            } else {
                errorMessage = result.ResultMessage || app.translate('Unknown POS error!');
            }

            throw new app.errors.Unprocessable(errorMessage);
        }

        await app
            .collection('finance.online-pos-receipts')
            .patch({_id: payload.onlinePOSReceiptId}, {status: 'canceled'}, {user: params.user});
    } catch (err) {
        let errorMessage;
        if (err.response && err.response.data) {
            errorMessage = err.response.data.ResultMessage || err.response.data.Message || err.message || app.translate('Unknown POS error!');
        } else {
            errorMessage = err.message || app.translate('Unknown POS error!');
        }
        throw new app.errors.Unprocessable(errorMessage);
    }
};

const cancel = async (app, payload, params) => {
    const {dealerCode, username, password, merchantKey} = JSON.parse(payload.pos.integrationParams);


    const opr = await app.collection('finance.online-pos-receipts').findOne({
        _id: payload.onlinePOSReceiptId,
        $select: ['_id', 'code', 'amount', 'referenceCode'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    const requestBody = {
        PaymentDealerAuthentication: {
            DealerCode: dealerCode,
            Username: username,
            Password: password,
            CheckKey: merchantKey
        },
        PaymentDealerRequest: {
            VirtualPosOrderId: opr.referenceCode,
            OtherTrxCode: opr.code
        }
    };

    try {
        const response = await axios.post('https://service.mokaunited.com/PaymentDealer/DoVoid', requestBody, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            })
        });

        const result = response.data;

        if (result.ResultCode !== 'Success') {
            let errorMessage;

            if (result.ResultCode === 'PaymentDealer.CheckPaymentDealerAuthentication.InvalidAccount') {
                errorMessage = app.translate('Invalid MokaPOS account credentials. Please check your DealerCode, Username, and Password.');
            } else if (result.ResultCode && result.ResultCode.includes('InvalidAccount')) {
                errorMessage = app.translate('Invalid MokaPOS account. Please check your account credentials.');
            } else if (result.ResultCode && result.ResultCode.includes('Authentication')) {
                errorMessage = app.translate('MokaPOS authentication error. Please check your account credentials.');
            } else {
                errorMessage = result.ResultMessage || app.translate('Unknown POS error!');
            }

            throw new app.errors.Unprocessable(errorMessage);
        }

        await app
            .collection('finance.online-pos-receipts')
            .patch({_id: payload.onlinePOSReceiptId}, {status: 'canceled'}, {user: params.user});
    } catch (err) {
        let errorMessage;
        if (err.response && err.response.data) {
            errorMessage = err.response.data.ResultMessage || err.response.data.Message || err.message || app.translate('Unknown POS error!');
        } else {
            errorMessage = err.message || app.translate('Unknown POS error!');
        }
        throw new app.errors.Unprocessable(errorMessage);
    }
};



const installments = async (app, payload) => {
    const {dealerCode, username, password, merchantKey} = JSON.parse(payload);


    const requestBody = {
        PaymentDealerAuthentication: {
            DealerCode: dealerCode,
            Username: username,
            Password: password,
            CheckKey: merchantKey
        }
    };

    try {
        const response = await axios.post('https://service.mokaunited.com/PaymentDealer/GetDealerPaymentPlanList', requestBody, {
            headers: {
                'Content-Type': 'application/json'
            },
            httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false,
                secureProtocol: 'TLSv1_2_method'
            })
        });

        const result = response.data;

        if (result.ResultCode !== 'Success' || !result.Data) {
            let errorMessage;

            if (result.ResultCode === 'PaymentDealer.CheckPaymentDealerAuthentication.InvalidAccount') {
                errorMessage = app.translate('Invalid MokaPOS account credentials. Please check your DealerCode, Username, and Password.');
            } else if (result.ResultCode && result.ResultCode.includes('InvalidAccount')) {
                errorMessage = app.translate('Invalid MokaPOS account. Please check your account credentials.');
            } else if (result.ResultCode && result.ResultCode.includes('Authentication')) {
                errorMessage = app.translate('MokaPOS authentication error. Please check your account credentials.');
            } else {
                errorMessage = result.ResultMessage || app.translate('Unknown POS error!');
            }

            throw new app.errors.Unprocessable(errorMessage);
        }

        const installments = [];

        if (Array.isArray(result.Data)) {
            result.Data.forEach(plan => {
                if (plan.CardType && plan.InstallmentList) {
                    installments.push({
                        cardBrand: plan.CardType,
                        installmentNo: 1,
                        rate: 0
                    });

                    plan.InstallmentList.forEach(inst => {
                        installments.push({
                            cardBrand: plan.CardType,
                            installmentNo: inst.InstallmentNumber,
                            rate: inst.CommissionRate
                        });
                    });
                }
            });
        }

        return installments;
    } catch (err) {
        let errorMessage;
        if (err.response && err.response.data) {
            errorMessage = err.response.data.ResultMessage || err.response.data.Message || err.message || app.translate('Unknown POS error!');
        } else {
            errorMessage = err.message || app.translate('Unknown POS error!');
        }
        throw new app.errors.Unprocessable(errorMessage);
    }
};

export default {
    charge,
    refund,
    cancel,
    list,
    installments
};
