<template>
    <ui-view
        ref="view"
        class="accounting-chart-of-accounts-view"
        type="table"
        :columns="columns"
        :items="items"
        :filters="filters"
        :loading="loading"
        :enable-sorting="false"
        :options="tableOptions"
        :right-panel-width="180"
        actions="create,remove"
        enable-search
        progress-id="system.configuration.chart-of-accounts"
        :single-select="singleSelect"
        :extra-actions="extraActions"
        :context-menu-actions="contextMenuActions"
        @create="handleCreate"
        @detail="handleDetail"
        @remove="handleRemove"
    >
        <div class="pt10 pl10 pr10" slot="right-panel">
            <div
                v-for="type in accountTypes"
                :key="type.name"
                :class="{selected: selectedType === type.name}"
                class="account-type mb10"
                @click="handleTypeSelect(type.name)"
            >
                <widget-statistic :amount="type.total" :description="type.label | t" />
            </div>

            <el-uploader
                ref="uploader"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                @started="$params('loading', true)"
                @completed="handleUploadComplete"
                v-show="false"
            />
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {firstUpper, toLower} from 'framework/helpers';

export default {
    data: () => ({
        items: null,
        loading: true,
        columns: [
            {
                field: 'name',
                label: 'Account',
                cellRenderer: 'agGroupCellRenderer',
                showRowGroup: true,
                suppressMenu: true,
                suppressMovable: true,
                cellRendererParams: {
                    suppressCount: true,
                    suppressDoubleClickExpand: true,
                    innerRenderer(params) {
                        const data = params.data;

                        if (_.isObject(data)) {
                            const account = `${data.code} - ${data.name}`;

                            if (data.tree.hasChild) {
                                return `<span class="text-primary text-semibold">${account}</span>`;
                            } else {
                                if (data.reconciliation) {
                                    return `<span class="text-success text-semibold">${account}</span>`;
                                }

                                return account;
                            }
                        }
                    }
                },
                getQuickFilterText(params) {
                    const data = params.data;

                    if (_.isObject(data)) {
                        return `${data.code} - ${data.name}`;
                    }
                }
            },
            {
                type: 'number',
                format: 'currency',
                field: 'balance',
                label: 'Balance',
                width: 150
            },
            {
                field: 'code',
                label: 'code',
                hidden: true
            }
        ],
        tableOptions: {
            treeData: true,
            //animateRows: false,
            groupDefaultExpanded: -1,
            groupSuppressAutoColumn: true,
            getDataPath(data) {
                return data.tree.path.split('/');
            }
        },
        accountTypes: [
            {name: 'assets', label: 'Assets', total: 0},
            {name: 'liabilities', label: 'Liabilities', total: 0},
            {name: 'equity', label: 'Equity', total: 0},
            {name: 'sales', label: 'Sales', total: 0},
            {name: 'costOfSales', label: 'Cost of Sales', total: 0},
            {name: 'operatingExpenses', label: 'Operating Expenses', total: 0},
            {name: 'financialExpenses', label: 'Financial Expenses', total: 0},
            {name: 'extraordinaryRevenuesAndExpenses', label: 'Extraordinary Revenues and Expenses', total: 0}
        ],
        selectedType: ''
    }),

    computed: {
        filters() {
            return this.selectedType ? {type: this.selectedType} : {};
        },
        singleSelect() {
            if (_.isBoolean(this.$params('singleSelect'))) {
                return !!this.$params('singleSelect');
            }

            return true;
        },
        extraActions() {
            return [
                {
                    name: 'export',
                    title: 'Export',
                    icon: 'fal fa-cloud-download',
                    handler: this.handleExport
                }
                // {
                //     name: 'import',
                //     title: 'Import',
                //     icon: 'fal fa-cloud-upload',
                //     handler: () => {
                //         this.$refs.uploader.$refs.upload.$refs['upload-inner'].handleClick();
                //     }
                // },
                // {
                //     name: 'update-paths',
                //     title: 'Update account paths',
                //     icon: 'fal fa-sync',
                //     handler: this.handleUpdatePaths
                // }
            ];
        },
        contextMenuActions() {
            return [
                {
                    title: this.$t('Create'),
                    disabled: false,
                    action: this.handleCreate
                },
                {
                    title: this.$t('Create a sub-account'),
                    disabled: () => !this.$params('selected') || this.$params('selected').length < 1,
                    action: async () => {
                        const selected = this.$params('selected')[0];

                        this.$program.dialog({
                            component: 'system.management.configuration.chart-of-accounts.detail',
                            params: {
                                parentPath: selected.tree.path,
                                isPreview: false
                            }
                        });
                    }
                },
                {
                    title: this.$t('Create a sibling account'),
                    disabled: () => !this.$params('selected') || this.$params('selected').length < 1,
                    action: async () => {
                        const selected = this.$params('selected')[0];
                        const parentItem = await this.$collection('kernel.accounts').parent(selected.tree.path, {
                            $select: ['tree'],
                            $disableLocalCache: true
                        });

                        this.$program.dialog({
                            component: 'system.management.configuration.chart-of-accounts.detail',
                            params: {
                                parentPath: parentItem.tree.path,
                                isPreview: false
                            }
                        });
                    }
                }
            ];
        }
    },

    methods: {
        async loadData(forRealTime = false) {
            if (!forRealTime) this.loading = true;

            const {accountsReport, typesReport} = await this.$rpc('accounting.reports-chart-of-accounts');
            let items = await this.$collection('kernel.accounts').find({$sort: {code: 1}});

            items = items.map(account => {
                account.balance = _.sumBy(accountsReport, r => {
                    if (r.path.indexOf(account.tree.path) !== -1) {
                        return r.debit - r.credit;
                    }

                    return 0;
                });

                return account;
            });

            this.items = items;

            this.accountTypes = this.accountTypes.map(type => {
                const r = typesReport.find(t => t.type === type.name);

                if (_.isObject(r)) {
                    type.total = r.total;
                }

                return type;
            });

            this.$nextTick(() => {
                if (_.isObject(this.$refs.view) && _.isObject(this.$refs.view.$refs.component)) {
                    this.$refs.view.$refs.component.api.redrawRows();
                }

                if (items.length < 1) {
                    this.$refs.view.$refs.component.api.showNoRowsOverlay();
                }

                if (!forRealTime) this.loading = true;
            });
        },
        async handleTypeSelect(typeName) {
            if (this.selectedType === typeName) {
                this.selectedType = '';
            } else {
                this.selectedType = typeName;
            }
        },
        async handleExport() {
            this.$params('loading', true);

            try {
                const result = await this.$rpc('system.export-chart-of-accounts');

                const link = document.createElement('a');
                link.href = this.$app.absoluteUrl(`temp-files/${result.file}?filename=${result.name}`);
                link.download = result.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleUpdatePaths() {
            this.$params('loading', true);

            try {
                await this.$rpc('system.update-chart-of-accounts-paths');
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleUploadComplete(payload) {
            this.$params('loading', true);

            try {
                this.items = [];

                await this.$rpc('system.import-chart-of-accounts', {
                    fileId: payload._id
                });

                this.$nextTick(async () => {
                    await this.loadData(true);

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                });
            } catch (error) {
                this.$program.message('error', error.message);

                this.$params('loading', false);
            }
        },
        handleCreate() {
            this.$program.dialog({
                component: 'system.management.configuration.chart-of-accounts.detail'
            });
        },
        handleDetail(data) {
            this.$program.dialog({
                component: 'system.management.configuration.chart-of-accounts.detail',
                params: {id: data._id, isPreview: false, checkAccount: this.checkAccount}
            });
        },
        async handleRemove() {
            const account = this.$params('selected')[0];

            if (account.system) {
                this.$params('selected', []);
                this.$program.message('error', this.$t('You cannot delete system accounts!'));

                return;
            }

            if (account.tree.hasChild) {
                this.$params('selected', []);
                this.$program.message('error', this.$t('The account has sub accounts. Please remove them first!'));

                return;
            }

            // Check if account has transaction.
            const transactionCount = await this.$collection('accounting.transactions').count({accountId: account._id});
            if (transactionCount > 0) {
                this.$params('selected', []);
                this.$program.message('error', this.$t('Accounts that have accounting entries can not be removed!'));

                return;
            }

            // Check.
            if (!(await this.checkAccount(account))) {
                return;
            }

            let params = {};
            params.collection = 'kernel.accounts';
            params.id = account._id;

            this.$rpc('kernel.common.remove-record', params)
                .then(async () => {
                    this.$params('selected', []);
                })
                .catch(error => {
                    this.$params('selected', []);
                    this.$program.message('error', error.message);
                });
        },
        async checkAccount(account) {
            // Check expense categories.
            const expenseCategory = await this.$collection('expense.expense-categories').findOne({
                accountId: account._id
            });
            if (!!expenseCategory) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the expense category {{name}}. Do you want to continue?', {
                            name: expenseCategory.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }
            if (this.$setting('expense.stampTaxAccountId') === account._id) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the expense settings. Do you want to continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check income categories.
            const incomeCategory = await this.$collection('income.income-categories').findOne({
                accountId: account._id
            });
            if (!!incomeCategory) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the income category {{name}}. Do you want to continue?', {
                            name: incomeCategory.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check journals.
            const journal = await this.$collection('accounting.journals').findOne({
                $or: [
                    {debitAccountId: account._id},
                    {creditAccountId: account._id},
                    {chequeCollectionAccountId: account._id},
                    {deniedChequeAccountId: account._id},
                    {chequeExecutionAccountId: account._id},
                    {chequeGuaranteeAccountId: account._id},
                    {chequeBankAccountId: account._id},
                    {promissoryNoteCollectionAccountId: account._id},
                    {protestedPromissoryNoteAccountId: account._id},
                    {promissoryNoteExecutionAccountId: account._id},
                    {promissoryNoteGuaranteeAccountId: account._id}
                ]
            });
            if (!!journal) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the journal {{name}}. Do you want to continue?', {
                            name: journal.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check texes.
            const tax = await this.$collection('kernel.taxes').findOne({
                $or: [{accountId: account._id}, {creditNoteAccountId: account._id}]
            });
            if (!!tax) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the tax {{name}}. Do you want to continue?', {
                            name: tax.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check loan models.
            const loanModel = await this.$collection('finance.loan-models').findOne({
                $or: [{loanAccountId: account._id}, {longTermAccountId: account._id}]
            });
            if (!!loanModel) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the loan model {{name}}. Do you want to continue?', {
                            name: loanModel.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check account set groups.
            const accountSet = await this.$collection('kernel.account-set-groups').findOne({
                'accounts.accountId': account._id
            });
            if (!!accountSet) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the account set group {{name}}. Do you want to continue?', {
                            name: accountSet.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check employee account set groups.
            const employeeAccountGroup = await this.$collection('hremployee-account-groups').findOne({
                'accounts.accountId': account._id
            });
            if (!!employeeAccountGroup) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t(
                            'The account is defined in the employee account group {{name}}. Do you want to continue?',
                            {
                                name: employeeAccountGroup.name
                            }
                        ),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check partner.
            const partner = await this.$collection('kernel.partners').findOne({
                $or: [
                    {accountingAccountId: account._id},
                    {additionalAllowanceAccount: account._id},
                    {employeeExpenseAccountId: account._id}
                ],
                $select: ['_id', 'name']
            });
            if (!!partner) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the partner {{name}}. Do you want to continue?', {
                            name: partner.name
                        }),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check period end template.
            const periodEndTemplate = await this.$collection('accounting.period-end-templates').findOne({
                $or: [{'accounts.sourceAccountId': account._id}, {'accounts.destinationAccountId': account._id}]
            });
            if (!!periodEndTemplate) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t(
                            'The account is defined in the period end template {{name}}. Do you want to continue?',
                            {
                                name: periodEndTemplate.name
                            }
                        ),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check reconciliation models.
            const reconciliationModel = await this.$collection('accounting.reconciliation-models').findOne({
                counterpartAccountId: account._id
            });
            if (!!reconciliationModel) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t(
                            'The account is defined in the reconciliation model {{name}}. Do you want to continue?',
                            {
                                name: reconciliationModel.name
                            }
                        ),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            // Check default accounts.
            const defaultAccount = (this.$app.setting('system.defaultAccounts') || []).find(
                a => a.accountId === account._id
            );
            if (!!defaultAccount) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('The account is defined in the default accounts. Do you want to continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return false;
            }

            return true;
        },
        refreshForRealTime() {
            this.loadData(true);
        }
    },

    async created() {
        this.refreshForRealTimeDebounced = _.debounce(this.refreshForRealTime, 150, {leading: false, trailing: true});

        await this.loadData();

        this.$collection('kernel.accounts').on('all', this.refreshForRealTimeDebounced);
        this.$collection('accounting.transactions').on('all', this.refreshForRealTimeDebounced);
    },

    beforeDestroy() {
        this.$collection('kernel.accounts').removeListener('all', this.refreshForRealTimeDebounced);
        this.$collection('accounting.transactions').removeListener('all', this.refreshForRealTimeDebounced);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.accounting-chart-of-accounts-view {
    .account-type {
        cursor: pointer;

        &:hover .widget-statistic {
            background-color: $primary-lighter;
            border-color: $primary;
        }

        &.selected .widget-statistic {
            background-color: $warning-lighter;
            border-color: $warning;
        }
    }
}
</style>
