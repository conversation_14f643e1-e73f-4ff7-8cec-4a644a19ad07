import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';

export default {
    name: 'travel-request',
    title: 'Travel Request',
    contentType: 'html',
    outputType: 'pdf',
    async getLocale(app, recordId) {
        if (recordId) {
            const document = await app.collection('expense.travel-requests').findOne({
                _id: recordId,
                $select: ['partnerId'],
                $disableSoftDelete: true
            });

            if (document) {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: document.partnerId,
                    $select: ['languageId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (partner) {
                    const language = await app.collection('kernel.languages').findOne({
                        _id: partner.languageId,
                        $select: ['isoCode'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (language) {
                        return language.isoCode;
                    }
                }
            }
        }

        const company = await app.collection('kernel.company').findOne({});

        return company.language.isoCode;
    },
    async record(app, recordId, params) {
        const d = await app.collection('expense.travel-requests').findOne({
            _id: recordId,
            $populate: [
                {
                    field: 'partner',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'branch',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'businessPurpose',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'partnerGroup',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'manager',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'department',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'travellingExpenseArea',
                    query: {$select: ['code', 'name']}
                },
                {
                    field: 'currency',
                    query: {$select: ['code', 'name']}
                }
            ],
            $disableSoftDelete: true
        });
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'requested', label: 'Requested'},
            {value: 'approved', label: 'Approved'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const data = {};

        const currency = await app.collection('kernel.currencies').findOne({
            _id: d.currencyId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const formatOptions = {
            currency: {
                symbol: currency.symbol,
                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            },
            useOutputPrecision: true
        };
        const f = (amount, type) => app.format(amount, type, formatOptions);

        data.code = d.code;
        const statusLabel = (statusOptions.find(option => option.value === d.status) || {}).label || '';
        if (!!statusLabel) {
            data.status = t(statusLabel);
        }
        if (_.isPlainObject(d.partner) && !_.isEmpty(d.partner)) {
            data.employeeCode = d.partner.code;
            data.employeeName = d.partner.name;
        }
        data.travelName = d.name;
        data.description = d.description;
        data.requestDate = _.isDate(d.requestDate) ? f(d.requestDate, 'date') : '';
        data.departureDate = _.isDate(d.departureDate) ? f(d.departureDate, 'date') : '';
        data.comebackDate = _.isDate(d.comebackDate) ? f(d.comebackDate, 'date') : '';
        data.employmentStartDate = _.isDate(d.employmentStartDate) ? f(d.employmentStartDate, 'date') : '';
        data.accommodation = f(d.accommodation, 'total');
        data.food = f(d.food, 'total');
        data.transportation = f(d.transportation, 'total');
        data.other = f(d.other, 'total');
        data.travellingExpenseAmount = f(d.travellingExpenseAmount, 'total');
        data.paymentAmount = f(d.paymentAmount, 'total');
        if (!!d.branch) {
            data.branchCode = d.branch.code;
            data.branchName = d.branch.name;
        }
        if (!!d.businessPurpose) {
            data.businessPurpose = d.businessPurpose.name;
        }
        if (!!d.partnerGroup) {
            data.employeeGroup = d.partnerGroup.name;
        }
        if (!!d.manager) {
            data.managerCode = d.manager.code;
            data.managerName = d.manager.name;
        }
        if (!!d.department) {
            data.departmentName = d.department.name;
        }
        if (!!d.travellingExpenseArea) {
            data.travellingExpenseArea = d.travellingExpenseArea.name;
        }
        if (!!d.currency) {
            data.currency = d.currency.name;
        }

        return data;
    },
    async sample(app, params) {
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const f = (amount, type) => app.format(amount, type);
        const document = {};

        document.code = 'TTTT';
        document.status = t('Draft');
        document.employeeCode = 'E00000045';
        document.employeeName = 'John Doe';
        document.travelName = 'Test travel';
        document.description = 'Test travel description';
        document.requestDate = f(app.datetime.local().toJSDate(), 'date');
        document.departureDate = f(app.datetime.local().toJSDate(), 'date');
        document.comebackDate = f(app.datetime.local().toJSDate(), 'date');
        document.employmentStartDate = f(app.datetime.local().toJSDate(), 'date');
        document.accommodation = f(100, 'total');
        document.food = f(100, 'total');
        document.transportation = f(100, 'total');
        document.other = f(100, 'total');
        document.travellingExpenseAmount = f(100, 'total');
        document.paymentAmount = f(100, 'total');
        document.branchCode = '01';
        document.branchName = 'Test branch';
        document.businessPurpose = 'Test business purpose';
        document.employeeGroup = 'Test group';
        document.managerCode = 'E0000145';
        document.managerName = 'Test manager';
        document.department = 'Test department';
        document.travellingExpenseArea = 'Test';
        document.currency = 'USD';

        return document;
    },
    content: (app, recordId) =>
        fs.readFile(path.join(app.config('paths.static'), 'templates/expense/travel-request.hbs'), {encoding: 'utf8'})
};
