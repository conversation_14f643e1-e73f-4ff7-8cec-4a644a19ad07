<template>
    <ui-view
        class="pcm-selection-constraint"
        type="form"
        :model="model"
        :schema="schema"
        :title="'Selection Constraint' | t"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        no-inner-padding
        full-height
        @changed="handleChange"
        v-if="isInitialized"
    >
        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="general" :label="'GENERAL' | t">
                <div class="constraint-wrapper">
                    <div class="constraint-top">
                        <ui-field
                            name="sourceFieldCode"
                            class="full-width mb0 mr10"
                            :options="sourceFieldOptions"
                            label-position="top"
                        />
                        <ui-field
                            name="destinationFieldCode"
                            class="full-width mb0 ml10"
                            :options="destinationFieldOptions"
                            label-position="top"
                        />
                    </div>

                    <div class="constraint-content">
                        <div class="constraint-source-options">
                            <ui-list
                                ref="sourceOptions"
                                :items="sourceOptions"
                                enable-search
                                label-from="label"
                                value-from="value"
                                disable-no-rows-overlay
                                single-select
                                @selected="handleSourceOptionSelect"
                            />
                        </div>

                        <div class="constraint-destination-options">
                            <el-transfer
                                :data="destinationOptions"
                                filterable
                                :filter-method="filterTransferItems"
                                :filter-placeholder="'Search' | t"
                                :titles="[$t('Options'), $t('Selected Options')]"
                                v-model="selectedDestinationOptions"
                                @change="handleDestinationOptionsSelect"
                                v-if="isDestinationOptionsShown && !!selectedSourceOptionValue"
                            />
                        </div>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="conditions" :label="'CONDITIONS' | t">
                <el-scrollbar class="p30">
                    <kernel-common-query-builder
                        :fields="queryBuilderFields"
                        stringify-query-output
                        v-model="model.condition"
                    />
                </el-scrollbar>
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import fastCopy from 'fast-copy';
import Random from 'framework/random';
import {toLower} from 'framework/helpers';

export default {
    data: () => ({
        model: {},
        extraFields: [
            'id',
            'stepCode',
            'stepName',
            'sourceFieldName',
            'destinationFieldName',
            'items',
            'condition',
            'isActive'
        ],
        attributes: [],
        attributeTypes: [],
        sourceOptions: [],
        destinationOptions: [],
        selectedSourceOptionValue: null,
        isDestinationOptionsShown: false,
        selectedDestinationOptions: [],
        activeTab: 'general',
        isInitialized: false
    }),

    computed: {
        schema() {
            return {
                id: {
                    type: 'string',
                    required: false
                },
                stepCode: {
                    type: 'string',
                    required: false
                },
                stepName: {
                    type: 'string',
                    required: false
                },
                sourceFieldCode: {
                    type: 'string',
                    label: 'Source field'
                },
                sourceFieldName: {
                    type: 'string',
                    required: false
                },
                destinationFieldCode: {
                    type: 'string',
                    label: 'Destination field'
                },
                destinationFieldName: {
                    type: 'string',
                    required: false
                },
                items: {
                    type: [
                        {
                            type: 'object',
                            required: false,
                            blackbox: true
                        }
                    ],
                    default: []
                },
                condition: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                isActive: {
                    type: 'boolean',
                    label: 'Is active',
                    default: true
                }
            };
        },
        sourceFieldOptions() {
            const attributes = this.attributes.filter(attribute => attribute.code !== this.model.destinationFieldCode);

            return attributes.map(attribute => ({
                value: attribute.code,
                label: attribute.name
            }));
        },
        destinationFieldOptions() {
            const attributes = this.attributes.filter(attribute => attribute.code !== this.model.sourceFieldCode);

            return attributes.map(attribute => ({
                value: attribute.code,
                label: attribute.name
            }));
        },
        queryBuilderFields() {
            const attributes = this.$params('attributes') || [];
            const attributeTypes = this.attributeTypes || [];
            const fields = [];

            for (const attribute of attributes) {
                const attributeType = attributeTypes.find(
                    attributeType => attributeType._id === attribute.attributeTypeId
                );
                if (!attributeType) {
                    continue;
                }

                const field = {
                    type: 'text',
                    name: attribute.code,
                    label: attribute.name
                };

                if (attributeType.fieldType === 'integer') {
                    field.type = 'integer';
                } else if (attributeType.fieldType === 'decimal' || attributeType.fieldType === 'money') {
                    field.type = 'decimal';
                } else if (attributeType.fieldType === 'date') {
                    field.type = 'date';
                } else if (attributeType.fieldType === 'datetime') {
                    field.type = 'datetime';
                } else if (attributeType.fieldType === 'time') {
                    field.type = 'time';
                } else if (attributeType.fieldType === 'boolean') {
                    field.type = 'decimal';
                } else if (attributeType.fieldType === 'yes-no') {
                    field.type = 'yes-no';
                }

                if (attributeType.fieldType === 'select' || attributeType.fieldType === 'select-multiple') {
                    field.options = (attributeType.options || [])
                        .filter(o => o.isActive)
                        .map(o => ({
                            value: o.value,
                            label: o.value
                        }));
                }

                fields.push(field);
            }

            return fields;
        }
    },

    methods: {
        async beforeInit(model) {
            if (!!model.sourceFieldCode && !!model.destinationFieldCode) {
                this.initOptions(model.sourceFieldCode, model.destinationFieldCode);
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            const attributes = fastCopy(this.attributes);

            if (!model.id) {
                this.model.id = Random.id(8);
            }
            this.model.stepCode = this.$params('step').code;
            this.model.stepName = this.$params('step').name;

            if (field === 'sourceFieldCode') {
                const attribute = attributes.find(attribute => attribute.code === model.sourceFieldCode);

                this.model.sourceFieldName = attribute.name;
            } else if (field === 'destinationFieldCode') {
                const attribute = attributes.find(attribute => attribute.code === model.destinationFieldCode);

                this.model.destinationFieldName = attribute.name;
            }

            if (
                (field === 'sourceFieldCode' || field === 'destinationFieldCode') &&
                !!model.sourceFieldCode &&
                !!model.destinationFieldCode
            ) {
                this.initOptions(model.sourceFieldCode, model.destinationFieldCode);
            }
        },
        handleSourceOptionSelect(selected) {
            this.isDestinationOptionsShown = false;

            this.$nextTick(() => {
                this.selectedSourceOptionValue = selected[0];

                const selectedSourceOptionValue = this.selectedSourceOptionValue;
                const selectionItem = fastCopy(this.model.items).find(
                    item => item.sourceOptionValue === selectedSourceOptionValue
                );
                if (!!selectionItem) {
                    this.selectedDestinationOptions = selectionItem.destinationOptionValues || [];
                } else {
                    this.selectedDestinationOptions = [];
                }

                this.$nextTick(() => {
                    this.isDestinationOptionsShown = true;
                });
            });
        },
        handleDestinationOptionsSelect(destinationOptionValues) {
            const selectedSourceOptionValue = this.selectedSourceOptionValue;
            if (!selectedSourceOptionValue) return;

            const items = (fastCopy(this.model.items) || []).filter(
                item => item.sourceOptionValue !== selectedSourceOptionValue
            );

            const item = {};
            item.sourceOptionValue = selectedSourceOptionValue;
            item.destinationOptionValues = destinationOptionValues;
            items.push(item);

            this.model.items = items;
        },
        initOptions(sourceFieldCode, destinationFieldCode) {
            if (!sourceFieldCode || !destinationFieldCode) {
                return;
            }

            const attributes = fastCopy(this.attributes);

            const sourceAttribute = attributes.find(attribute => attribute.code === sourceFieldCode);
            const destinationAttribute = attributes.find(attribute => attribute.code === destinationFieldCode);
            const sourceOptions = [];
            const destinationOptions = [];

            for (const option of sourceAttribute.attributeType.options || []) {
                sourceOptions.push({
                    value: option.value,
                    label: option.value
                });
            }

            for (const option of destinationAttribute.attributeType.options || []) {
                destinationOptions.push({
                    key: option.value,
                    label: option.value
                });
            }

            this.sourceOptions = sourceOptions;
            this.destinationOptions = destinationOptions;

            if (this.sourceOptions.length > 0) {
                setTimeout(() => {
                    this.$refs.sourceOptions.select(this.sourceOptions[0].value);
                }, 150);
            }
        },
        filterTransferItems(query, item) {
            return toLower(item.label).indexOf(toLower(query)) >= 0;
        }
    },

    async created() {
        this.$params('loading', true);

        this.attributeTypes = await this.$collection('pcm.attribute-types').find({
            _id: {$in: (this.$params('attributes') || []).map(attr => attr.attributeTypeId)},
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        const attributes = [];
        if ((this.$params('attributes') || []).length > 0) {
            const attributeTypes = this.attributeTypes;

            for (const attribute of fastCopy(this.$params('attributes') || [])) {
                const attributeType = attributeTypes.find(
                    attributeType => attributeType._id === attribute.attributeTypeId
                );

                if (attributeType.fieldType !== 'select') {
                    continue;
                }

                attribute.attributeType = attributeType;

                attributes.push(attribute);
            }
        }
        this.attributes = attributes;
        this.isInitialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.pcm-selection-constraint .constraint-wrapper {
    display: flex;
    flex-flow: column nowrap;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .constraint-top {
        display: flex;
        flex-flow: row nowrap;
        flex: 0 0 82px;
        padding: 20px;
        border-bottom: 1px solid $border-color;
    }

    .constraint-content {
        position: relative;
        flex: 1 1 0;
        padding-left: 270px;

        .constraint-source-options {
            position: absolute;
            top: 0;
            left: 0;
            width: 270px;
            height: 100%;
            border-right: 1px solid $border-color;
        }

        .constraint-destination-options {
            width: 100%;
            height: 100%;
            padding: 20px;
        }
    }
}
</style>
