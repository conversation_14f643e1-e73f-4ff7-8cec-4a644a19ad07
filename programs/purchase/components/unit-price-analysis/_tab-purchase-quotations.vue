<template>
    <div class="sales-unit-price-analysis">
        <div class="analysis-result">
            <div class="analysis-result-scope">
                <ui-scope
                    id="sales-unit-price-analysis-purchase-quotations"
                    :applied-items="appliedScopeItems"
                    :filters="scopeApplicableFilters"
                    @changed="handleScopeChange"
                    @initialized="scopeInitialized = true"
                />
            </div>

            <div class="analysis-result-table">
                <ui-table
                    id="sales-unit-price-analysis-purchase-quotations"
                    :columns="columns"
                    :filters="filters"
                    :getRows="getRows"
                    :enable-detail="false"
                    :summary-row="summaryRow"
                    @selected="handleSelect"
                    v-if="activeTab === 'purchase-quotations' && scopeInitialized"
                />
            </div>
        </div>

        <div class="analysis-form">
            <ui-form :model="model" :schema="schema" @changed="handleChange" v-if="!$params('isPreview')">
                <ui-field
                    name="changeType"
                    label-position="top"
                    :options="changeTypeOptions"
                    translate-labels
                    style="flex: 0 0 100px"
                />
                <ui-field
                    name="operation"
                    label-position="top"
                    :options="operationOptions"
                    translate-labels
                    style="flex: 0 0 100px"
                />
                <ui-field name="value" :precision="4" label-position="top" style="flex: 0 0 100px" />
                <ui-field
                    name="unitPrice"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="currentUnitPrice"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    disabled
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="unitPriceToBeApplied"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    disabled
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>

                <el-button
                    type="primary"
                    class="mb10"
                    size="mini"
                    style="height: 24px; margin-top: 19px; width: 120px"
                    icon="far fa-check"
                    @click="handleApplyUnitPrice"
                >
                    {{ $t('Apply') }}
                </el-button>
            </ui-form>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery} from 'framework/helpers';

export default {
    props: {
        productIds: Array,
        currencyFormat: Object,
        currencyId: String,
        currencyName: String,
        isForeignCurrency: Boolean,
        partnerId: String,
        unitPrice: Number,
        handleApply: Function,
        activeTab: String
    },

    data: () => ({
        scopeQuery: {},
        totalReport: {},
        model: {},
        changeTypeOptions: [
            {value: 'linear', label: 'Linear'},
            {value: 'percentage', label: 'Percentage'}
        ],
        scopeInitialized: false
    }),

    computed: {
        filters() {
            return fastCopy(this.scopeQuery);
        },
        appliedScopeItems() {
            return [];
        },
        scopeApplicableFilters() {
            return [
                {code: 'today', label: 'Today', query: 'date|today'},
                {code: 'yesterday', label: 'Yesterday', query: 'date|yesterday'},
                {code: 'thisWeek', label: 'This week', query: 'date|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'date|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'date|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'date|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'date|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'date|lastQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'date|thisYear'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'date|lastYear'},

                {
                    field: 'date',
                    code: 'date',
                    label: 'Date',
                    type: 'date',
                    operator: 'in-range',
                    allowedOperators: ['in-range']
                },
                {
                    field: 'documentCode',
                    label: 'Document code'
                },
                {
                    field: 'documentTypeId',
                    label: 'Document type',
                    collection: 'purchase.document-types',
                    relationParams: params => {
                        return {
                            id: params.data.documentTypeId,
                            view: 'purchase.configuration.document-types-detail'
                        };
                    },
                    filters: {
                        type: 'quotation',
                        $sort: {name: 1}
                    }
                },
                {
                    code: 'partnerId',
                    field: 'partnerId',
                    label: 'Vendor',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'vendor', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'partnerGroupId',
                    label: 'Vendor group',
                    collection: 'kernel.partner-groups',
                    filters: {type: 'vendor', $sort: {name: 1}}
                },
                {
                    field: 'productId',
                    label: 'Product',
                    collection: 'inventory.products',
                    labelFrom: 'displayName',
                    filters: {$sort: {code: 1}, canBeSold: true}
                },
                {
                    field: 'barcode',
                    label: 'Barcode'
                },
                {
                    field: 'brandId',
                    label: 'Brand',
                    collection: 'inventory.product-brands'
                },
                {
                    field: 'manufacturerProductCode',
                    label: 'Manufacturer product code'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition: () => {
                        return this.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'payment-planned', label: 'Payment Planned', color: 'success'},
                        {value: 'converted-to-order', label: 'Converted To Order'},
                        {value: 'converted-to-invoice', label: 'Converted To Invoice'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                }
            ];
        },
        columns() {
            return [
                {
                    field: 'date',
                    label: 'Date',
                    sort: 'desc',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'documentCode',
                    label: 'Document',
                    relationParams: params => {
                        const data = params.data;

                        return {
                            id: data.documentId,
                            view: 'purchase.purchase.quotations-detail'
                        };
                    },
                    width: 180
                },
                {
                    field: 'documentType',
                    label: 'Document Type',
                    relationParams: params => {
                        const data = params.data;
                        return {
                            id: data.documentTypeId,
                            view: 'purchase.configuration.document-types-detail'
                        };
                    },
                    width: 180
                },
                {
                    field: 'partnerName',
                    label: 'Vendor',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            view: 'partners.partners-detail',
                            id: data.partnerId,
                            customLabel: data.partnerName
                        };
                    },
                    sortable: false,
                    minWidth: 210
                },
                {
                    field: 'productCode',
                    label: 'Product code',
                    view: 'inventory.catalog.products-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.productId
                        };
                    },
                    visible: false,
                    width: 180
                },
                {
                    field: 'productDefinition',
                    label: 'Product definition',
                    view: 'inventory.catalog.products-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.partnerId
                        };
                    },
                    visible: false,
                    minWidth: 210
                },
                {
                    field: 'barcode',
                    label: 'Barcode',
                    visible: false,
                    width: 150
                },
                {
                    field: 'manufacturerProductCode',
                    label: 'Manufacturer product code',
                    visible: false,
                    width: 150
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    format: 'unit',
                    width: 120
                },
                {
                    field: 'unitPrice',
                    label: 'Unit price',
                    format: 'unit-price',
                    formatOptions: () => {
                        return this.currencyFormat;
                    },
                    width: 150
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'payment-planned', label: 'Payment Planned', color: 'success'},
                        {value: 'converted-to-order', label: 'Converted To Order', color: 'primary'},
                        {value: 'converted-to-invoice', label: 'Converted To Invoice', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 150
                }
            ];
        },
        schema() {
            return {
                changeType: {
                    type: 'string',
                    label: 'Change type',
                    default: 'linear'
                },
                operation: {
                    type: 'string',
                    label: 'Operation',
                    default: 'multiply'
                },
                value: {
                    type: 'decimal',
                    label: 'Value',
                    default: 1
                },
                unitPrice: {
                    type: 'decimal',
                    label: 'Unit price',
                    default: this.unitPrice
                },
                currentUnitPrice: {
                    type: 'decimal',
                    label: 'Current unit price',
                    default: this.unitPrice
                },
                unitPriceToBeApplied: {
                    type: 'decimal',
                    label: 'Unit price to be applied',
                    default: this.unitPrice
                }
            };
        },
        operationOptions() {
            if (this.model.changeType === 'percentage') {
                return [
                    {value: 'add', label: 'Add'},
                    {value: 'subtract', label: 'Subtract'}
                ];
            } else {
                return [
                    {value: 'multiply', label: 'Multiply'},
                    {value: 'divide', label: 'Divide'},
                    {value: 'add', label: 'Add'},
                    {value: 'subtract', label: 'Subtract'}
                ];
            }
        }
    },

    methods: {
        async getRows(tableQuery, params) {
            const query = fastCopy(rawMongoQuery(tableQuery));
            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const limit = endRow - startRow;
            const skip = startRow;
            const result = {
                total: 0,
                data: []
            };

            const pipeline = [
                {
                    $match: {
                        code: {$ne: this.$params('documentCode')},
                        'items.productId': {$in: this.productIds}
                    }
                },
                {
                    $unwind: '$items'
                },
                {
                    $match: {
                        'items.productId': {$in: this.productIds}
                    }
                },
                {
                    $project: {
                        _id: {$concat: ['$code', '$items.id']},
                        date: '$quotationDate',
                        documentId: '$_id',
                        documentCode: '$code',
                        documentTypeId: '$documentTypeId',
                        partnerId: '$partnerId',
                        partnerGroupId: '$partnerGroupId',
                        productId: '$items.productId',
                        productCode: '$items.productCode',
                        productDefinition: '$items.productDefinition',
                        barcode: '$items.barcode',
                        brandId: '$items.brandId',
                        branchId: '$branchId',
                        unitId: '$items.baseUnitId',
                        status: '$status',
                        manufacturerProductCode: '$items.manufacturerProductCode',
                        quantity: '$items.baseQuantity',
                        unitPrice: this.isForeignCurrency
                            ? {
                                  $divide: [
                                      {
                                          $multiply: [
                                              '$currencyRate',
                                              {
                                                  $multiply: [
                                                      {$divide: ['$items.realTotal', '$items.quantity']},
                                                      {$divide: ['$items.baseQuantity', '$items.quantity']}
                                                  ]
                                              }
                                          ]
                                      },
                                      `$exchangeRatesMap.${this.currencyName}`
                                  ]
                              }
                            : {
                                  $multiply: [
                                      '$currencyRate',
                                      {
                                          $multiply: [
                                              {$divide: ['$items.realTotal', '$items.quantity']},
                                              {$divide: ['$items.baseQuantity', '$items.quantity']}
                                          ]
                                      }
                                  ]
                              }
                    }
                },
                {
                    $match: query
                }
            ];
            const resultReport = await this.$collection('purchase.quotations').aggregate([
                ...pipeline.concat(!!tableQuery.$sort ? [{$sort: tableQuery.$sort}] : []),
                {$skip: skip},
                {$limit: limit}
            ]);
            const totalReport = await this.$collection('purchase.quotations').aggregate([
                ...pipeline,
                {
                    $group: {
                        _id: null,
                        count: {$sum: 1},
                        quantity: {$sum: '$quantity'},
                        unitPrice: {$sum: {$multiply: ['$unitPrice', '$quantity']}}
                    }
                }
            ]);
            if (totalReport.length > 0) {
                totalReport[0].unitPrice = totalReport[0].unitPrice / totalReport[0].quantity;
            }
            this.totalReport = totalReport.length > 0 ? totalReport[0] : {};
            result.total = totalReport.length > 0 ? totalReport[0].count : 0;
            result.data = resultReport;

            if (result.data.length > 0) {
                const partners = await this.$collection('kernel.partners').find({
                    _id: {$in: result.data.map(item => item.partnerId)},
                    $select: ['_id', 'code', 'name'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                const documentTypes = await this.$collection('purchase.document-types').find({
                    _id: {$in: result.data.map(item => item.documentTypeId).filter(id => !!id)},
                    $select: ['_id', 'name'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                result.data = result.data.map(item => {
                    const partner = partners.find(partner => partner._id === item.partnerId);

                    if (!!partner) {
                        item.partnerCode = partner.code;
                        item.partnerName = partner.name;
                    }

                    const documentType = documentTypes.find(dt => dt._id === item.documentTypeId);
                    if (!!documentType) {
                        item.documentType = documentType.name;
                    }

                    return item;
                });
            }

            if (totalReport.length > 0) {
                this.updateUnitPrice(totalReport[0].unitPrice);
            } else {
                this.updateUnitPrice(this.unitPrice);
            }

            return result;
        },
        async summaryRow() {
            return {
                rowCount: this.totalReport.count,
                quantity: this.totalReport.quantity,
                unitPrice: this.totalReport.unitPrice
            };
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handleSelect(items) {
            if (items.length > 0) {
                let totalPrice = 0;
                let totalQty = 0;

                for (const item of items) {
                    totalPrice += item.unitPrice * item.quantity;
                    totalQty += item.quantity;
                }

                this.updateUnitPrice(totalPrice / totalQty);
            } else {
                this.updateUnitPrice(this.unitPrice);
            }
        },
        handleChange(model, field) {
            if (field === 'changeType') {
                if (model.changeType === 'linear') {
                    this.model.operation = 'multiply';
                    this.model.value = 1;
                } else if (model.changeType === 'base-price') {
                    this.model.operation = 'multiply';
                    this.model.value = 1;
                } else if (model.changeType === 'percentage') {
                    this.model.operation = 'add';
                    this.model.value = 0;
                }
            } else if (field === 'operation') {
                if (model.operation === 'multiply') {
                    this.model.value = 1;
                } else if (model.operation === 'divide') {
                    this.model.value = 1;
                } else if (model.operation === 'add') {
                    this.model.value = 0;
                } else if (model.operation === 'subtract') {
                    this.model.value = 0;
                }
            }

            let price = model.unitPrice;
            if (model.changeType === 'linear') {
                if (model.operation === 'multiply') price *= model.value || 0;
                else if (model.operation === 'divide') price /= model.value || 1;
                else if (model.operation === 'add') price += model.value || 0;
                else if (model.operation === 'subtract') price -= model.value || 0;
            } else if (model.changeType === 'percentage') {
                if (model.operation === 'add') price += (price * (model.value || 0)) / 100;
                else if (model.operation === 'subtract') price -= (price * (model.value || 0)) / 100;
            }
            price = this.$app.round(price, 'unit-price');
            this.model.unitPriceToBeApplied = price;
        },
        handleApplyUnitPrice() {
            this.handleApply(this.model.unitPriceToBeApplied);
            this.$dialog.close();
        },
        updateUnitPrice(unitPrice = 0) {
            this.model.changeType = 'linear';
            this.model.operation = 'multiply';
            this.model.value = 1;
            this.model.unitPrice = this.$app.round(unitPrice, 'unit-price');
            this.model.unitPriceToBeApplied = this.$app.round(unitPrice, 'unit-price');
        }
    }
};
</script>
