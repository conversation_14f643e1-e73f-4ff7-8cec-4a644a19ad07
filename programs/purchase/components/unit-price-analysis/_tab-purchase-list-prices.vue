<template>
    <div class="sales-unit-price-analysis">
        <div class="analysis-result">
            <div class="analysis-result-scope">
                <ui-scope
                    id="sales-unit-price-analysis-purchase-list-prices"
                    :applied-items="appliedScopeItems"
                    :filters="scopeApplicableFilters"
                    @changed="handleScopeChange"
                    @initialized="scopeInitialized = true"
                />
            </div>

            <div class="analysis-result-table">
                <ui-table
                    id="sales-unit-price-analysis-purchase-list-prices"
                    :columns="columns"
                    :filters="filters"
                    :getRows="getRows"
                    :enable-detail="false"
                    :summary-row="summaryRow"
                    @selected="handleSelect"
                    v-if="activeTab === 'purchase-list-prices' && scopeInitialized"
                />
            </div>
        </div>

        <div class="analysis-form">
            <ui-form :model="model" :schema="schema" @changed="handleChange" v-if="!$params('isPreview')">
                <ui-field
                    name="changeType"
                    label-position="top"
                    :options="changeTypeOptions"
                    translate-labels
                    style="flex: 0 0 100px"
                />
                <ui-field
                    name="operation"
                    label-position="top"
                    :options="operationOptions"
                    translate-labels
                    style="flex: 0 0 100px"
                />
                <ui-field name="value" :precision="4" label-position="top" style="flex: 0 0 100px" />
                <ui-field
                    name="unitPrice"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="currentUnitPrice"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    disabled
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field
                    name="unitPriceToBeApplied"
                    :precision="$setting('system.unitPricePrecision')"
                    label-position="top"
                    disabled
                    style="flex: 0 0 150px"
                >
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>

                <el-button
                    type="primary"
                    class="mb10"
                    size="mini"
                    style="height: 24px; margin-top: 19px; width: 120px"
                    icon="far fa-check"
                    @click="handleApplyUnitPrice"
                >
                    {{ $t('Apply') }}
                </el-button>
            </ui-form>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery} from 'framework/helpers';

export default {
    props: {
        productIds: Array,
        currencyFormat: Object,
        currencyId: String,
        currencyName: String,
        isForeignCurrency: Boolean,
        partnerId: String,
        unitPrice: Number,
        handleApply: Function,
        activeTab: String
    },

    data: () => ({
        scopeQuery: {},
        totalReport: {},
        model: {},
        changeTypeOptions: [
            {value: 'linear', label: 'Linear'},
            {value: 'percentage', label: 'Percentage'}
        ],
        scopeInitialized: false
    }),

    computed: {
        filters() {
            return fastCopy(this.scopeQuery);
        },
        appliedScopeItems() {
            if (!!this.partnerId) {
                return [
                    {
                        type: 'filter',
                        payload: {
                            code: 'partnerId',
                            value: [this.partnerId]
                        }
                    }
                ];
            }

            return [];
        },
        scopeApplicableFilters() {
            return [
                {
                    code: 'partnerId',
                    field: 'partnerId',
                    label: 'Partner',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'vendor', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'listPriceId',
                    label: 'List price',
                    collection: 'purchase.list-prices',
                    filters: {
                        status: 'published',
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'validFrom',
                    code: 'validFrom',
                    label: 'Valid from',
                    type: 'date',
                    operator: 'in-range',
                    allowedOperators: ['in-range']
                },
                {
                    field: 'validTo',
                    code: 'validTo',
                    label: 'Valid to',
                    type: 'date',
                    operator: 'in-range',
                    allowedOperators: ['in-range']
                },
                {
                    field: 'currencyId',
                    label: 'Currency',
                    collection: 'kernel.currencies',
                    filters: {
                        $sort: {name: 1}
                    }
                }
            ];
        },
        columns() {
            return [
                {
                    field: 'listPriceName',
                    label: 'Price list',
                    relationParams: params => {
                        const data = params.data;

                        return {
                            id: data.listPriceId,
                            view: 'purchase.pricing.list-prices-detail'
                        };
                    },
                    width: 250
                },
                {
                    field: 'validFrom',
                    label: 'Valid from',
                    format: 'date',
                    width: 180
                },
                {
                    field: 'validTo',
                    label: 'Valid to',
                    format: 'date',
                    width: 180
                },
                {
                    field: 'price',
                    label: 'Price',
                    format: 'unit-price',
                    formatOptions: () => {
                        return this.currencyFormat;
                    },
                    width: 120
                },
                {
                    field: 'grossPrice',
                    label: 'Gross price',
                    format: 'unit-price',
                    formatOptions: () => {
                        return this.currencyFormat;
                    },
                    width: 120
                },
                {
                    field: 'currencyName',
                    label: 'Currency',
                    width: 100
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'published', label: 'Published', color: 'success'},
                        {value: 'unpublished', label: 'Unpublished', color: 'danger'}
                    ],
                    width: 120
                }
            ];
        },
        schema() {
            return {
                changeType: {
                    type: 'string',
                    label: 'Change type',
                    default: 'linear'
                },
                operation: {
                    type: 'string',
                    label: 'Operation',
                    default: 'multiply'
                },
                value: {
                    type: 'decimal',
                    label: 'Value',
                    default: 1
                },
                unitPrice: {
                    type: 'decimal',
                    label: 'Unit price',
                    default: this.unitPrice
                },
                currentUnitPrice: {
                    type: 'decimal',
                    label: 'Current unit price',
                    default: this.unitPrice
                },
                unitPriceToBeApplied: {
                    type: 'decimal',
                    label: 'Unit price to be applied',
                    default: this.unitPrice
                }
            };
        },
        operationOptions() {
            if (this.model.changeType === 'percentage') {
                return [
                    {value: 'add', label: 'Add'},
                    {value: 'subtract', label: 'Subtract'}
                ];
            } else {
                return [
                    {value: 'multiply', label: 'Multiply'},
                    {value: 'divide', label: 'Divide'},
                    {value: 'add', label: 'Add'},
                    {value: 'subtract', label: 'Subtract'}
                ];
            }
        }
    },

    methods: {
        async getRows(tableQuery, params) {
            const query = fastCopy(rawMongoQuery(tableQuery));
            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const limit = endRow - startRow;
            const skip = startRow;
            const result = {
                total: 0,
                data: []
            };

            const listPriceItems = await this.$collection('purchase.list-price-items').find({
                productId: {$in: this.productIds},
                $select: ['_id', 'listPriceId', 'productId', 'price', 'grossPrice', 'validFrom', 'validTo']
            });

            if (listPriceItems.length === 0) {
                return result;
            }

            const listPriceIds = _.uniq(listPriceItems.map(item => item.listPriceId));

            const listPrices = await this.$collection('purchase.list-prices').find({
                _id: {$in: listPriceIds},
                $select: ['_id', 'name', 'currencyId', 'status']
            });


            if (listPrices.length === 0) {
                return result;
            }

            const currencyIds = _.uniq(listPrices.map(list => list.currencyId));
            const currencies = await this.$collection('kernel.currencies').find({
                _id: {$in: currencyIds},
                $select: ['_id', 'name']
            });


            const listPriceMap = _.keyBy(listPrices, '_id');
            const currencyMap = _.keyBy(currencies, '_id');

            const combinedData = listPriceItems.map(item => {
                const listPrice = listPriceMap[item.listPriceId];
                if (!listPrice) return null;

                const currency = currencyMap[listPrice.currencyId];

                return {
                    _id: item._id,
                    listPriceId: item.listPriceId,
                    listPriceName: listPrice.name,
                    productId: item.productId,
                    price: item.price,
                    grossPrice: item.grossPrice,
                    validFrom: item.validFrom,
                    validTo: item.validTo,
                    currencyId: listPrice.currencyId,
                    currencyName: currency ? currency.name : '',
                    status: listPrice.status
                };
            }).filter(item => item !== null);


            let filteredData = combinedData;
            if (Object.keys(query).length > 0) {
                filteredData = combinedData.filter(item => {
                    for (const key in query) {
                        if (item[key] !== query[key]) {
                            return false;
                        }
                    }
                    return true;
                });
            }

            if (tableQuery.$sort) {
                const sortField = Object.keys(tableQuery.$sort)[0];
                const sortDirection = tableQuery.$sort[sortField];

                filteredData.sort((a, b) => {
                    if (a[sortField] < b[sortField]) return sortDirection === 1 ? -1 : 1;
                    if (a[sortField] > b[sortField]) return sortDirection === 1 ? 1 : -1;
                    return 0;
                });
            }

            const paginatedData = filteredData.slice(skip, skip + limit);

            let avgPrice = 0;
            if (filteredData.length > 0) {
                avgPrice = filteredData.reduce((sum, item) => sum + item.price, 0) / filteredData.length;
            }

            result.total = filteredData.length;
            result.data = paginatedData;

            this.totalReport = {
                count: filteredData.length,
                price: avgPrice
            };

            if (filteredData.length > 0) {
                this.updateUnitPrice(avgPrice);
            } else {
                this.updateUnitPrice(this.unitPrice);
            }

            return result;
        },
        async summaryRow() {
            return {
                rowCount: this.totalReport.count,
                price: this.totalReport.price
            };
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handleSelect(items) {
            if (items.length > 0) {
                let totalPrice = 0;

                for (const item of items) {
                    totalPrice += item.price;
                }

                this.updateUnitPrice(totalPrice / items.length);
            } else {
                this.updateUnitPrice(this.unitPrice);
            }
        },
        handleChange(model, field) {
            if (field === 'changeType') {
                if (model.changeType === 'linear') {
                    this.model.operation = 'multiply';
                    this.model.value = 1;
                } else if (model.changeType === 'percentage') {
                    this.model.operation = 'add';
                    this.model.value = 0;
                }
            } else if (field === 'operation') {
                if (model.operation === 'multiply') {
                    this.model.value = 1;
                } else if (model.operation === 'divide') {
                    this.model.value = 1;
                } else if (model.operation === 'add') {
                    this.model.value = 0;
                } else if (model.operation === 'subtract') {
                    this.model.value = 0;
                }
            }

            let price = model.unitPrice;
            if (model.changeType === 'linear') {
                if (model.operation === 'multiply') price *= model.value || 0;
                else if (model.operation === 'divide') price /= model.value || 1;
                else if (model.operation === 'add') price += model.value || 0;
                else if (model.operation === 'subtract') price -= model.value || 0;
            } else if (model.changeType === 'percentage') {
                if (model.operation === 'add') price += (price * (model.value || 0)) / 100;
                else if (model.operation === 'subtract') price -= (price * (model.value || 0)) / 100;
            }
            price = this.$app.round(price, 'unit-price');
            this.model.unitPriceToBeApplied = price;
        },
        handleApplyUnitPrice() {
            this.handleApply(this.model.unitPriceToBeApplied);
            this.$dialog.close();
        },
        updateUnitPrice(unitPrice = 0) {
            this.model.changeType = 'linear';
            this.model.operation = 'multiply';
            this.model.value = 1;
            this.model.unitPrice = this.$app.round(unitPrice, 'unit-price');
            this.model.unitPriceToBeApplied = this.$app.round(unitPrice, 'unit-price');
        }
    }
};
</script>
