<template>
    <ui-view type="content" :title="title" v-if="isInitialized">
        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane
                v-for="type in allowedUnitPriceAnalysisTypeOptions"
                :key="type.value"
                :name="type.value"
                :label="type.label"
            >
                <component
                    :is="`tab-${type.value}`"
                    :product-ids="productIds"
                    :currency-format="currencyFormat"
                    :currency-id="currencyId"
                    :currency-name="currencyName"
                    :is-foreign-currency="isForeignCurrency"
                    :partner-id="partnerId"
                    :lead-id="leadId"
                    :unit-price="unitPrice"
                    :handle-apply="handleApply"
                    :active-tab="activeTab"
                />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {toUpper} from 'framework/helpers';

import TabSalesQuotations from './unit-price-analysis/_tab-sales-quotations';
import TabSalesOrders from './unit-price-analysis/_tab-sales-orders';
import TabCustomerInvoices from './unit-price-analysis/_tab-customer-invoices';
import TabPurchaseQuotations from './unit-price-analysis/_tab-purchase-quotations';
import TabPurchaseOrders from './unit-price-analysis/_tab-purchase-orders';
import TabVendorInvoices from './unit-price-analysis/_tab-vendor-invoices';
import TabSalesPriceLists from './unit-price-analysis/_tab-sales-price-lists';
import TabPurchaseListPrices from './unit-price-analysis/_tab-purchase-list-prices';

export default {
    data: () => ({
        productIds: [],
        currencyId: '',
        currencyName: '',
        isForeignCurrency: false,
        currencyFormat: null,
        partnerId: '',
        leadId: '',
        unitPrice: 0,
        handleApply: 0,
        activeTab: 'purchase-orders',
        isInitialized: false
    }),

    computed: {
        title() {
            const productCode = this.$params('productCode');
            const productDefinition = this.$params('productDefinition');

            return `${this.$t('Unit Price Analysis')} / ${productCode} - ${productDefinition}`;
        },
        allowedUnitPriceAnalysisTypeOptions() {
            return [
                {value: 'purchase-quotations', label: 'Purchase quotations'},
                {value: 'purchase-orders', label: 'Purchase orders'},
                {value: 'vendor-invoices', label: 'Vendor Invoices'},
                {value: 'sales-quotations', label: 'Sales quotations'},
                {value: 'sales-orders', label: 'Sales orders'},
                {value: 'customer-invoices', label: 'Customer invoices'},
                {value: 'sales-price-lists', label: 'Sales price lists'},
                {value: 'purchase-list-prices', label: 'Purchase list prices'}
            ]
                .map(o => {
                    o.label = toUpper(this.$t(o.label));

                    return o;
                })
                .filter(o => this.$params('allowedUnitPriceAnalysisTypes').includes(o.value));
        }
    },

    methods: {},

    async created() {
        const company = this.$store.getters['session/company'];
        const product = await this.$collection('inventory.products').findOne({
            _id: this.$params('productId'),
            $select: ['_id', 'barcode', 'barcodes'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        let productIds = [product._id];
        let barcodes = [];
        if (!!product.barcode) {
            barcodes.push(product.barcode);
        }
        if (Array.isArray(product.barcodes) && product.barcodes.length > 0) {
            barcodes.push(...product.barcodes.map(b => b.barcode));
        }
        barcodes = _.uniq(barcodes);
        if (barcodes.length > 0) {
            const products = await this.$collection('inventory.products').find({
                $or: [{barcode: {$in: barcodes}}, {'barcodes.barcode': {$in: barcodes}}],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            productIds.push(...products.map(p => p._id));
        }
        productIds = _.uniq(productIds);

        this.productIds = productIds;
        this.currencyId = this.$params('currencyId');
        this.currencyName = (
            await this.$collection('kernel.currencies').findOne({
                _id: this.currencyId,
                $disableSoftDelete: true,
                $disableActiveCheck: true
            })
        ).name;
        this.currencyFormat = this.$params('currencyFormat');
        this.isForeignCurrency = this.$params('currencyId') !== company.currencyId;
        this.partnerId = this.$params('partnerId');
        this.leadId = this.$params('leadId');
        this.unitPrice = this.$params('unitPrice');
        this.handleApply = this.$params('handleApply');

        this.$nextTick(() => {
            this.isInitialized = true;
        });
    },

    components: {
        TabSalesQuotations,
        TabSalesOrders,
        TabCustomerInvoices,
        TabPurchaseQuotations,
        TabPurchaseOrders,
        TabVendorInvoices,
        TabSalesPriceLists,
        TabPurchaseListPrices
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.sales-unit-price-analysis {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .analysis-result {
        position: relative;
        flex: 1;
        padding-top: 45px;

        .analysis-result-scope {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 0 8.5px;
            height: 45px;
        }

        .analysis-result-table {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
    }

    .analysis-form {
        .ui-form {
            display: flex;
            flex-flow: row nowrap;

            .ui-field {
                margin-right: 12px;
            }
        }
    }
}
</style>
