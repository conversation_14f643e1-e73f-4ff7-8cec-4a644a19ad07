import _ from 'lodash';
import schema from './schema';
import fastCopy from 'fast-copy';
import TotalsCurrencyConversions from './_totals-currency-conversions';

export default {
    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            return this.$t('New Quotation');
        },
        schema() {
            return schema(this);
        },
        statuses() {
            const model = this.model;
            const statuses = [{value: 'draft', label: 'Draft'}];

            if (_.isObject(model.paymentPlan) && !_.isEmpty(model.paymentPlan)) {
                statuses.push({
                    value: 'payment-planned',
                    label: 'Payment Planned'
                });
            }

            if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else if (this.status === 'converted-to-invoice') {
                statuses.push({
                    value: 'converted-to-invoice',
                    label: 'Converted To Invoice'
                });
            } else {
                statuses.push({
                    value: 'converted-to-order',
                    label: 'Converted To Order'
                });
            }

            return statuses;
        },
        status() {
            const model = this.model;

            return model.status;
        },
        assignation() {
            const approvedDocumentsCanBeAssigned = !!this.$setting('system.approvedDocumentsCanBeAssigned');
            const disabled = approvedDocumentsCanBeAssigned
                ? this.status === 'canceled'
                : this.status === 'converted-to-order' ||
                  this.status === 'converted-to-invoice' ||
                  this.status === 'canceled' ||
                  (!!this.model.workflowApprovalStatus && this.model.workflowApprovalStatus === 'waiting-for-approval');

            return {
                organizationScope: 'purchase',
                managerField: 'purchaseManagerId',
                employeeField: 'purchaseRepresentativeId',
                disabled
            };
        },
        actions() {
            return this.$params('id') &&
                (this.isConvertedToOrder ||
                    this.isConvertedToInvoice ||
                    this.isInvoiced ||
                    this.isCanceled ||
                    (!!this.model.workflowApprovalStatus &&
                        this.model.workflowApprovalStatus === 'waiting-for-approval'))
                ? 'edit:disabled,cancel'
                : 'edit,cancel';
        },
        extraActions() {
            const self = this;

            return [
                {
                    name: 'clear-payment-plan',
                    title: 'Clear Payment Plan',
                    icon: 'fal fa-broom',
                    handler: this.handleClearPaymentPlan,
                    disabled() {
                        return (
                            self.$params('isPreview') ||
                            self.status === 'draft' ||
                            self.status === 'converted-to-order' ||
                            self.status === 'converted-to-invoice' ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'cancel-quotation',
                    title: 'Cancel Quotation',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    disabled() {
                        if (!!self.$user.isRoot) {
                            return !self.$params('id') || self.status === 'canceled';
                        }

                        return (
                            !self.$params('id') ||
                            self.status === 'converted-to-order' ||
                            self.status === 'converted-to-invoice' ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                }
            ];
        },
        isConvertedToOrder() {
            const model = this.model;

            return model.status === 'converted-to-order';
        },
        isConvertedToInvoice() {
            const model = this.model;

            return model.status === 'converted-to-invoice';
        },
        isCanceled() {
            const model = this.model;

            return model.status === 'canceled';
        },
        totalItems() {
            const self = this;
            const model = this.model;
            const items = [];

            // Sub total
            items.push({label: this.$t('Subtotal'), value: model.subTotal});

            // Discount
            if (this.$setting('purchase.discounts')) {
                let discountLabel =
                    model.discount > 0
                        ? `${this.$t('Discount')} (%${this.$format(model.discount, 'percentage')})`
                        : this.$t('Discount');
                const item = {
                    label: discountLabel,
                    value: model.discountAmount > 0 ? -model.discountAmount : 0,
                    action: {
                        title: this.$t('Apply Discount'),
                        icon: 'fas fa-badge-percent text-danger',
                        disabled:
                            model.subTotal === 0 ||
                            (this.$params('id') && this.$params('isPreview')) ||
                            this.model.status === 'payment-planned',
                        handler: this.applyDiscount
                    }
                };

                if (model.discountAmount > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Subtotal After Discount'),
                            value: model.subTotalAfterDiscount
                        }
                    ];
                }

                let totalRowDiscountAmount = 0;
                for (const row of model.items || []) {
                    const unDiscountedTotal = this.$app.round(row.quantity * row.unitPrice, 'total');
                    const rowDiscountAmount = this.$app.round(unDiscountedTotal - row.total, 'total');

                    if (rowDiscountAmount > 0) {
                        totalRowDiscountAmount += rowDiscountAmount;
                    }
                }
                if (totalRowDiscountAmount > 0) {
                    if (!Array.isArray(item.subItems)) item.subItems = [];

                    item.subItems.push({
                        label: this.$t('Total Row Discount Amount'),
                        value: totalRowDiscountAmount
                    });
                }

                items.push(item);
            }

            // Tax total.
            if (Array.isArray(model.appliedTaxes) && model.appliedTaxes.length > 0) {
                const groupedTaxes = _.groupBy(model.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));
                const item = {
                    label: this.$t('Taxes'),
                    value: model.taxTotal,
                    subItems: []
                };

                for (const label of Object.keys(groupedTaxes)) {
                    const appliedTaxes = groupedTaxes[label];
                    let total = 0;

                    for (const tax of appliedTaxes) {
                        total += tax.appliedAmount;
                    }

                    item.subItems.push({
                        label,
                        value: this.$app.round(total, 'total')
                    });
                }

                items.push(item);
            } else {
                items.push({label: this.$t('Taxes'), value: 0});
            }

            items.push({
                label: this.$t('Rounding'),
                value: model.rounding,
                action: {
                    title: this.$t('Apply Rounding'),
                    icon: 'far fa-badge',
                    disabled: model.subTotal === 0 || (this.$params('id') && this.$params('isPreview')),
                    handler: this.applyRounding
                }
            });

            // Grand total
            items.push({
                label: this.$t('Grand Total'),
                value: model.grandTotal,
                style: {fontWeight: 700},
                action: {
                    title: this.$t('Currency Conversions'),
                    icon: 'fas fa-sync-alt',
                    iconStyle: 'font-size: 12px;',
                    popupComponent: TotalsCurrencyConversions,
                    popupWidth: 320,
                    popupPayload: {
                        systemCurrencyId: this.systemCurrencyId,
                        currencies: this.currencies ?? [],
                        currencyRate: this.model.currencyRate ?? 1,
                        exchangeRates: this.model.exchangeRates ?? []
                    }
                }
            });

            if (!!this.$setting('system.freight')) {
                const freight = model.freight;
                let amount = 0;

                if (!!freight && freight.amount > 0) {
                    amount = freight.amount;
                }

                const item = {
                    label: this.$t('Freight'),
                    value: amount,
                    action: {
                        title: this.$t('Apply Freight'),
                        icon: 'fas fa-truck',
                        iconStyle: {fontSize: '12px'},
                        handler: this.applyFreight
                    }
                };

                if (!!freight && Array.isArray(freight.appliedTaxes) && freight.appliedTaxes.length > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Freight'),
                            value: amount
                        }
                    ];

                    const groupedTaxes = _.groupBy(freight.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));

                    for (const label of Object.keys(groupedTaxes)) {
                        const appliedTaxes = groupedTaxes[label];
                        let total = 0;

                        for (const tax of appliedTaxes) {
                            total += tax.appliedAmount;
                        }

                        item.subItems.push({
                            label,
                            value: this.$app.round(total, 'total')
                        });
                    }
                }

                items.push(item);
            }

            return {items, format: this.currencyFormat};
        },
        documentTypeIdFilters() {
            const filters = {type: 'quotation'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.documentTypeIds || []
                };
            }

            return filters;
        },
        partnerGroupIdFilters() {
            const filters = {type: 'vendor'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.vendorGroupIds || []
                };
            }

            return filters;
        },
        partnerIdFilters() {
            const filters = {type: 'vendor'};

            if (this.model.partnerGroupId) {
                filters.groupId = this.model.partnerGroupId;
            } else if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupId = {
                    $in: this.organizationSettings.vendorGroupIds || []
                };
            }

            return filters;
        },
        isPartnerIdDisabled() {
            // if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
            //     return !this.model.partnerGroupId || this.status === 'payment-planned';
            // }

            return this.status === 'payment-planned';
        },
        paymentTermIdFilters() {
            const contractParams = this.model.contractParams;
            const filters = {};

            if (_.isPlainObject(contractParams) && Array.isArray(contractParams.paymentTermIds)) {
                filters._id = {$in: contractParams.paymentTermIds};
            } else if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.paymentTermIds || []
                };
            }

            filters.$or = [{scope: null}, {scope: {$exists: false}}, {scope: 'payment'}];

            return filters;
        },
        listPriceIdFilters() {
            const filters = {status: 'published'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.listPriceIds || []
                };
            }

            return filters;
        },
        guaranteeIdFilters() {
            const contractParams = this.model.contractParams;
            const filters = {
                type: {$in: ['partner-received', 'partner-issued']},
                partnerId: this.model.partnerId,
                status: 'approved'
            };

            if (_.isPlainObject(contractParams) && Array.isArray(contractParams.guaranteeIds)) {
                filters._id = {$in: contractParams.guaranteeIds};
            }

            return filters;
        },
        activityPayload() {
            return {
                partnerType: 'vendor',
                partnerId: this.model.partnerId
            };
        },
        itemsTableOptions() {
            const self = this;

            return {
                rowClassRules: {
                    'bg-purple'(params) {
                        return _.isPlainObject(params.data) && !!params.data.contractApplied;
                    },
                    'bg-orange'(params) {
                        return (
                            _.isPlainObject(params.data) &&
                            !params.data.contractApplied &&
                            !!params.data.discountListApplied
                        );
                    }
                }
            };
        },
        itemsContextMenuActions() {
            return [
                {
                    title: this.$t('Stock Status'),
                    icon: 'inventory',
                    disabled: row => {
                        return !(!!row.product && !!row.warehouseId);
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.stock-status.master',
                            params: {
                                productId: row.productId,
                                warehouseId: row.warehouseId,
                                isADPGraphShown: true
                            }
                        });
                    }
                },
                {
                    title: this.$t('Expiration Analysis'),
                    icon: 'calendar-exclamation',
                    disabled: row => {
                        return !(!!row.product && row.product.tracking === 'serial');
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.expiration-analysis',
                            params: {
                                productId: row.productId
                            }
                        });
                    }
                },
                {
                    title: this.$t('Unit Price Analysis'),
                    icon: 'coins',
                    disabled: row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('purchase.allowedUnitPriceAnalysisTypes');

                        return !(
                            !!row.product &&
                            Array.isArray(allowedUnitPriceAnalysisTypes) &&
                            allowedUnitPriceAnalysisTypes.length > 0
                        );
                    },
                    action: async row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('purchase.allowedUnitPriceAnalysisTypes');

                        this.$program.dialog({
                            component: 'purchase.components.unit-price-analysis',
                            params: {
                                documentCollection: 'purchase.quotations',
                                documentId: this.$params('id'),
                                documentCode: this.model.code,
                                partnerId: this.model.partnerId,
                                leadId: null,
                                productId: row.productId,
                                productCode: row.product.code,
                                productDefinition: row.product.definition,
                                unitPrice: row.unitPrice,
                                currencyId: this.model.currencyId,
                                currencyRate: this.model.currencyRate,
                                currencyFormat: this.currencyFormat,
                                allowedUnitPriceAnalysisTypes,
                                forcedPreview: this.$params('isPreview'),
                                handleApply: async unitPrice => {
                                    this.$params('loading', true);

                                    const itemIndex = this.model.items.findIndex(item => item.id === row.id);
                                    const updatedItems = await this.$rpc('purchase.decorate-quotation-items', {
                                        items: [
                                            {
                                                ...this.model.items[itemIndex],
                                                unitId: row.baseUnitId,
                                                unitPrice
                                            }
                                        ],
                                        field: 'unitId',
                                        model: _.omit(this.model, 'items'),
                                        productFields: this.productFields,
                                        dontSetUnitPrice: true
                                    });

                                    const items = [...this.model.items];
                                    items[itemIndex] = updatedItems[0];

                                    this.$set(this.model, 'items', items);
                                    this.afterSaveItem();

                                    this.$params('loading', false);
                                }
                            }
                        });
                    }
                },
                ...(this.$app.hasModule('pcm')
                    ? [
                          {
                              title: this.$t('Configure Product'),
                              icon: 'cog',
                              disabled: row => {
                                  return !(!!row._id && !!row.productId && !!row.pcmModelId);
                              },
                              action: async row => {
                                  this.$params('loading', true);

                                  try {
                                      const onSubmit = async ({
                                          configuratorType,
                                          description,
                                          configurationId,
                                          deliveryDate,
                                          quantity,
                                          price,
                                          additionalPrice,
                                          products,
                                          hash
                                      }) => {
                                          this.$params('loading', true);

                                          try {
                                              const items = fastCopy(this.model.items);
                                              const itemsIndex = items.findIndex(i => i.id === row.id);

                                              if (configuratorType !== 'product-finder') {
                                                  const item = (
                                                      await this.$rpc('purchase.decorate-quotation-items', {
                                                          items: [
                                                              {
                                                                  ...row,
                                                                  description,
                                                                  pcmConfigurationId: configurationId,
                                                                  pcmHash: hash,
                                                                  scheduledDate: deliveryDate,
                                                                  quantity,
                                                                  baseQuantity:
                                                                      quantity * (row.baseQuantity / row.quantity)
                                                                  // unitPrice:
                                                                  //     quantity > 0
                                                                  //         ? this.$app.round(
                                                                  //               price / quantity,
                                                                  //               'unit-price'
                                                                  //           )
                                                                  //         : 0
                                                              }
                                                          ],
                                                          field: 'quantity',
                                                          model: _.omit(this.model, 'items'),
                                                          productFields: this.productFields
                                                      })
                                                  )[0];

                                                  items[itemsIndex] = item;
                                              } else {
                                                  items.splice(itemsIndex, 1);
                                              }

                                              if (products.length > 0) {
                                                  const additionalItems = await this.$rpc(
                                                      'purchase.decorate-quotation-items',
                                                      {
                                                          items: products.map(row => {
                                                              return {
                                                                  productId: row.productId,
                                                                  scheduledDate: deliveryDate,
                                                                  quantity: row.quantity,
                                                                  baseQuantity: row.quantity,
                                                                  unitPrice: row.unitPrice,
                                                                  discount: 0,
                                                                  unitPriceAfterDiscount: 0,
                                                                  grossUnitPriceAfterDiscount: 0,
                                                                  taxTotal: 0,
                                                                  grossTotal: 0,
                                                                  stockQuantity: 0,
                                                                  orderedQuantity: 0,
                                                                  assignedQuantity: 0,
                                                                  availableQuantity: 0,
                                                                  warehouseStockQuantity: 0,
                                                                  warehouseOrderedQuantity: 0,
                                                                  warehouseAssignedQuantity: 0,
                                                                  warehouseAvailableQuantity: 0,
                                                                  total: 0
                                                              };
                                                          }),
                                                          field: 'productId',
                                                          model: _.omit(this.model, 'items'),
                                                          productFields: this.productFields,
                                                          dontSetUnitPrice: true
                                                      }
                                                  );

                                                  items.push(...additionalItems);
                                              }

                                              this.model.items = items;

                                              // Reset totals.
                                              this.model.discount = await this.calculateGeneralDiscount();
                                              this.model.paymentPlan = null;
                                              this.model.paymentPlanningDate = null;
                                              await this.calculateTotals();

                                              this.$nextTick(() => {
                                                  this.itemsKey = _.uniqueId('quotationItems_');

                                                  this.$program.message(
                                                      'success',
                                                      this.$t('Product configuration applied successfully.')
                                                  );
                                              });
                                          } catch (error) {
                                              this.$program.message('error', error.message);
                                          }

                                          this.$params('loading', false);
                                      };
                                      const params = {
                                          title: row.productDefinition,
                                          forcedPreview: this.$params('isPreview'),
                                          modelId: row.pcmModelId,
                                          price: row.unitPrice,
                                          quantity: row.quantity,
                                          deliveryDate: row.scheduledDate,
                                          configurationId: row.pcmConfigurationId,
                                          currencyFormat: this.currencyFormat,
                                          currencyId: this.model.currencyId,
                                          currencyRate: this.model.currencyRate || 1,
                                          listPriceId: this.model.listPriceId,
                                          issueDate: this.model.quotationDate,
                                          referenceCollection: 'purchase.quotations',
                                          referenceView: 'purchase.purchase.quotations',
                                          referenceId: this.$params('id'),
                                          referenceCode: this.model.code,
                                          partnerId: this.model.partnerId,
                                          onSubmit
                                      };

                                      if (!!row.pcmConfigurationId) {
                                          const configuration = await this.$collection('pcm.configurations').findOne({
                                              _id: row.pcmConfigurationId,
                                              $disableSoftDelete: true,
                                              $disableActiveCheck: true
                                          });

                                          params.price = configuration.payload.price;
                                          params.quantity = configuration.payload.quantity;
                                          params.deliveryDate = configuration.payload.deliveryDate;
                                          params.values = configuration.payload.values;
                                          params.stepId = configuration.payload.stepId;
                                          params.finder = configuration.payload.finder;
                                      }

                                      this.$program.dialog({
                                          component: 'pcm.components.configuration',
                                          params
                                      });
                                  } catch (error) {
                                      this.$program.message('error', error.message);
                                  }
                              }
                          }
                      ]
                    : [])
            ];
        },
        financialProjectIdFilters() {
            return {
                $and: [
                    {
                        $or: [
                            {validFrom: {$exists: false}},
                            {validFrom: {$eq: null}},
                            {validFrom: {$lte: this.model.quotationDate}}
                        ]
                    },
                    {
                        $or: [
                            {validTo: {$exists: false}},
                            {validTo: {$eq: null}},
                            {validTo: {$gte: this.model.quotationDate}}
                        ]
                    }
                ],
                $sort: {code: 1}
            };
        },
        warehouseIdFilters() {
            const filters = {branchId: this.model.branchId};

            if (
                _.isObject(this.organizationSettings) &&
                !_.isEmpty(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.warehouseIds) &&
                this.organizationSettings.warehouseIds.length > 0
            ) {
                filters._id = {
                    $in: this.organizationSettings.warehouseIds || []
                };
            }

            return filters;
        }
    }
};
