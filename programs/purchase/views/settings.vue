<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :before-init="beforeInit"
        :before-submit="beforeSubmit"
        :after-submit="afterSubmit"
        actions="edit"
    >
        <div class="columns">
            <div class="column is-half">
                <ui-legend title="General" />
                <ui-field
                    name="purchaseOrganizations"
                    description="Enable purchase organizations."
                    field-type="compound-fields"
                />
                <ui-field name="defaultQuotationValidityDayCount" />
            </div>

            <div class="column is-half">
                <ui-legend title="Pricing" />
                <ui-field
                    name="purchaseListPrice"
                    description="Enable purchase list prices."
                    field-type="compound-fields"
                />
                <ui-field
                    name="lineDiscounts"
                    description="Apply manual discounts on quotation, order and invoice lines."
                    field-type="compound-fields"
                />
                <ui-field
                    name="discounts"
                    description="Shows a general discount form under the quotation, order and invoice lines."
                    field-type="compound-fields"
                />
                <ui-field
                    name="allowedUnitPriceAnalysisTypes"
                    :options="allowedUnitPriceAnalysisTypeOptions"
                    translate-labels
                    class="mt10"
                />
            </div>
        </div>
    </ui-view>
</template>

<script>
export default {
    data: () => ({
        model: {},
        allowedUnitPriceAnalysisTypeOptions: [
            {value: 'purchase-quotations', label: 'Purchase quotations'},
            {value: 'purchase-orders', label: 'Purchase orders'},
            {value: 'vendor-invoices', label: 'Vendor Invoices'},
            {value: 'sales-quotations', label: 'Sales quotations'},
            {value: 'sales-orders', label: 'Sales orders'},
            {value: 'customer-invoices', label: 'Customer invoices'},
            {value: 'sales-price-lists', label: 'Sales price lists'},
            {value: 'purchase-list-prices', label: 'Purchase list prices'}
        ]
    }),

    computed: {
        schema() {
            return {
                purchaseOrganizations: {
                    type: 'boolean',
                    label: 'Purchase organizations'
                },
                defaultQuotationValidityDayCount: {
                    type: 'integer',
                    label: 'Default quotation validity day count',
                    min: 0
                },
                purchaseListPrice: {
                    type: 'boolean',
                    label: 'Purchase list price'
                },
                lineDiscounts: {
                    type: 'boolean',
                    label: 'Line discounts'
                },
                discounts: {
                    type: 'boolean',
                    label: 'Discounts'
                },
                allowedUnitPriceAnalysisTypes: {
                    type: ['string'],
                    label: 'Allowed unit price analysis types',
                    default: []
                }
            };
        }
    },

    methods: {
        async beforeInit(model) {
            model.purchaseOrganizations = this.$setting('purchase.purchaseOrganizations');
            model.defaultQuotationValidityDayCount = this.$setting('purchase.defaultQuotationValidityDayCount');
            model.purchaseListPrice = this.$setting('purchase.purchaseListPrice');
            model.lineDiscounts = this.$setting('purchase.lineDiscounts');
            model.discounts = this.$setting('purchase.discounts');
            model.allowedUnitPriceAnalysisTypes = this.$setting('purchase.allowedUnitPriceAnalysisTypes');

            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async afterSubmit() {
            await this.$rpc('kernel.common.save-settings', {program: 'purchase', settings: this.$raw(this.model)});
        }
    }
};
</script>
