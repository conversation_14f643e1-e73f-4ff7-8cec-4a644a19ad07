import _ from 'lodash';

export default {
    name: 'save-quotation',
    async action({data, id}, params) {
        const app = this.app;
        const isCreate = !id;
        const quotationsCollection = app.collection('purchase.quotations');

        // Check items.
        if (Array.isArray(data.items)) {
            for (const item of data.items) {
                // Check product.
                if (!item.productId) {
                    throw new app.errors.Unprocessable(this.translate('Each row must have a product defined!'));
                }

                // Check row total.
                if (item.total < 0) {
                    throw new app.errors.Unprocessable(this.translate('Row total cannot be lower than zero!'));
                }
            }
        }

        // Exchange rates map.
        const exchangeRatesMap = {};
        for (const exchangeRate of data.exchangeRates || []) {
            exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
        }
        data.exchangeRatesMap = exchangeRatesMap;

        // Init limit params.
        data.limitParams = await app.rpc('finance.check-partner-limit', {
            partnerId: data.partnerId,
            currencyId: data.currencyId,
            guaranteeId: data.guaranteeId,
            amount: data.grandTotal,
            document: 'quotation'
        });

        if (!isCreate) {
            // Get existing quotation.
            const quotation = await quotationsCollection.findOne({
                _id: id,
                $select: ['status', 'items']
            });

            // Check if we have an quotation with this id.
            if (!_.isObject(quotation)) {
                throw new app.errors.Unprocessable(this.translate('Quotation not found!'));
            }

            // Check if quotation is already approved or canceled.
            if (
                quotation.status === 'converted-to-order' ||
                quotation.status === 'converted-to-invoice' ||
                quotation.status === 'canceled'
            ) {
                throw new app.errors.Unprocessable(
                    this.translate('Converted or canceled quotations cannot be updated!')
                );
            }

            // Check if document has at least one item.
            if (
                (quotation.status === 'converted-to-order' || quotation.status === 'converted-to-invoice') &&
                quotation.items.length < 1
            ) {
                throw new app.errors.Unprocessable(this.translate('Document rows must have at least one item!'));
            }

            // Check product warehouse blockage.
            if (data.status === 'converted-to-order' || data.status === 'converted-to-invoice') {
                const pwb = await app.rpc('inventory.get-product-warehouse-blockage', data.items);

                if (!!pwb) {
                    throw new app.errors.Unprocessable(pwb.message);
                }
            }

            // Remove partner limit transaction if canceled.
            if (data.status === 'canceled') {
                await app.collection('finance.partner-limit-transactions').remove({
                    documentId: id,
                    documentCollection: 'purchase.quotations'
                });
            }

            // Run assignation, and validation workflow.
            if (app.hasModule('workflow')) {
                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'purchase.quotations',
                        data: data,
                        id,
                        operation: 'update',
                        actionTypes: ['assignation', 'validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            // Update quotation.
            const result = await quotationsCollection.patch({_id: id}, data, {
                user: params.user,
                checkPermission: true
            });

            // Create partner limit.
            if (data.status === 'approved') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: quotation.partnerId,
                    $select: ['currencyId', 'enableLimitChecks', 'limitControlDocument'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (partner.enableLimitChecks && partner.limitControlDocument === 'quotation') {
                    const limit = {};

                    limit.partnerType = 'vendor';
                    limit.partnerId = partner._id;
                    limit.guaranteeId = data.guaranteeId;
                    limit.date = app.datetime.local().toJSDate();
                    limit.documentCollection = 'purchase.quotations';
                    limit.documentView = 'purchase.purchase.quotations-detail';
                    limit.documentId = id;
                    limit.currencyId = partner.currencyId;
                    limit.amount = -data.grandTotal;

                    await app.collection('finance.partner-limit-transactions').create(limit, {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    });
                }
            }

            // Run notification workflow.
            if (app.hasModule('workflow')) {
                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'purchase.quotations',
                        data: result,
                        id,
                        operation: 'update',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            return result;
        } else {
            // Run validation workflow.
            if (app.hasModule('workflow')) {
                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'purchase.quotations',
                        data: {
                            ...data,
                            extra: {
                                basePrice: 0,
                                grossProfit: 0,
                                profitRate: 0,
                                profitMargin: 0,
                                cashAmount: 0,
                                cashInstallmentCount: 0,
                                moneyTransferAmount: 0,
                                moneyTransferInstallmentCount: 0,
                                chequeAmount: 0,
                                chequeInstallmentCount: 0,
                                promissoryNoteAmount: 0,
                                promissoryNoteInstallmentCount: 0,
                                posAmount: 0,
                                posInstallmentCount: 0,
                                installmentCount: 0
                            }
                        },
                        operation: 'create',
                        actionTypes: ['validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            // Create and return quotation.
            const result = await quotationsCollection.create(data, {
                user: params.user,
                checkPermission: true
            });

            // Create partner limit.
            if (data.status === 'approved') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: data.partnerId,
                    $select: ['currencyId', 'enableLimitChecks', 'limitControlDocument'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (partner.enableLimitChecks && partner.limitControlDocument === 'quotation') {
                    const limit = {};

                    limit.partnerType = 'vendor';
                    limit.partnerId = partner._id;
                    limit.guaranteeId = data.guaranteeId;
                    limit.date = app.datetime.local().toJSDate();
                    limit.documentCollection = 'purchase.quotations';
                    limit.documentView = 'purchase.purchase.quotations-detail';
                    limit.documentId = result._id;
                    limit.currencyId = partner.currencyId;
                    limit.amount = -data.grandTotal;

                    await app.collection('finance.partner-limit-transactions').create(limit, {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    });
                }
            }

            // Run notification workflow.
            if (app.hasModule('workflow')) {
                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'purchase.quotations',
                        data: result,
                        operation: 'create',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            // PCM.
            if (app.hasModule('pcm')) {
                const operations = [];

                for (const item of result.items.filter(item => !!item.pcmConfigurationId)) {
                    operations.push({
                        updateOne: {
                            filter: {_id: item.pcmConfigurationId},
                            update: {$set: {referenceId: result._id}}
                        }
                    });
                }

                if (operations.length > 0) {
                    await app.collection('pcm.configurations').bulkWrite(operations);
                }
            }

            return result;
        }
    }
};
