export default {
    name: 'fixtures',
    title: 'Fixtures',
    branch: 'single',
    schema: {
        // General
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true
        },
        status: {
            type: 'string',
            label: 'Status',
            default: 'draft',
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        productId: {
            type: 'string',
            label: 'Product',
            required: false,
            index: true
        },
        productCode: {
            type: 'string',
            label: 'Product code',
            required: false,
            index: true
        },
        productDefinition: {
            type: 'string',
            label: 'Product definition',
            required: false,
            index: true
        },
        assetTypeId: {
            type: 'string',
            label: 'Asset type',
            index: true
        },
        assetTypeCode: {
            type: 'string',
            label: 'Asset type code',
            required: false,
            index: true
        },
        assetTypeName: {
            type: 'string',
            label: 'Asset type name',
            required: false,
            index: true
        },
        assetCategoryId: {
            type: 'string',
            label: 'Asset category',
            index: true
        },
        assetCategoryCode: {
            type: 'string',
            label: 'Asset category code',
            required: false,
            index: true
        },
        assetCategoryName: {
            type: 'string',
            label: 'Asset category name',
            required: false,
            index: true
        },
        acquisitionMethod: {
            type: 'string',
            label: 'Acquisition method',
            default: 'purchase',
            index: true
        },
        assetClassId: {
            type: 'string',
            label: 'Asset class',
            index: true
        },
        assetClassCode: {
            type: 'string',
            label: 'Asset class code',
            required: false,
            index: true
        },
        assetClassName: {
            type: 'string',
            label: 'Asset class name',
            required: false,
            index: true
        },
        departmentId: {
            type: 'string',
            label: 'Department',
            required: false,
            index: true
        },
        departmentCode: {
            type: 'string',
            label: 'Department code',
            required: false,
            index: true
        },
        departmentName: {
            type: 'string',
            label: 'Department name',
            required: false,
            index: true
        },
        departmentPath: {
            type: 'string',
            label: 'Department path',
            required: false,
            index: true
        },
        workingGroupId: {
            type: 'string',
            label: 'Working group',
            required: false,
            index: true
        },
        workingGroupCode: {
            type: 'string',
            label: 'Working group code',
            required: false,
            index: true
        },
        workingGroupName: {
            type: 'string',
            label: 'Working group name',
            required: false,
            index: true
        },
        usagePurposeId: {
            type: 'string',
            label: 'Usage purpose',
            required: false,
            index: true
        },
        usagePurposeCode: {
            type: 'string',
            label: 'Usage purpose code',
            required: false,
            index: true
        },
        usagePurposeName: {
            type: 'string',
            label: 'Usage purpose name',
            required: false,
            index: true
        },
        brandId: {
            type: 'string',
            label: 'Brand',
            required: false,
            index: true
        },
        brandCode: {
            type: 'string',
            label: 'Brand code',
            required: false,
            index: true
        },
        brandName: {
            type: 'string',
            label: 'Brand name',
            required: false,
            index: true
        },
        modelId: {
            type: 'string',
            label: 'Model',
            required: false,
            index: true
        },
        modelCode: {
            type: 'string',
            label: 'Model code',
            required: false,
            index: true
        },
        modelName: {
            type: 'string',
            label: 'Model name',
            required: false,
            index: true
        },
        image: {
            type: 'string',
            label: 'Image',
            required: false
        },
        branchCode: {
            type: 'string',
            label: 'Branch code',
            required: false,
            index: true
        },
        branchName: {
            type: 'string',
            label: 'Branch name',
            required: false,
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false
        },
        financialProjectCode: {
            type: 'string',
            label: 'Project code',
            required: false
        },
        financialProjectName: {
            type: 'string',
            label: 'Project name',
            required: false
        },
        responsibleEmployeeId: {
            type: 'string',
            label: 'Responsible employee',
            required: false,
            index: true
        },
        responsibleEmployeeCode: {
            type: 'string',
            label: 'Responsible employee code',
            required: false,
            index: true
        },
        responsibleEmployeeName: {
            type: 'string',
            label: 'Responsible employee name',
            required: false,
            index: true
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        entryDate: {
            type: 'date',
            label: 'Entry date',
            required: false,
            index: true
        },
        releaseDate: {
            type: 'date',
            label: 'Release date',
            required: false,
            index: true
        },
        rentalStartDate: {
            type: 'datetime',
            label: 'Rental start date',
            required: false,
            index: true
        },
        rentalEndDate: {
            type: 'datetime',
            label: 'Rental end date',
            required: false,
            index: true
        },
        fixtureNo: {
            type: 'string',
            label: 'Fixture no',
            index: true
        },
        barcode: {
            type: 'string',
            label: 'Barcode',
            required: false,
            index: true
        },
        serialNumber: {
            type: 'string',
            label: 'Serial number',
            unique: true,
            index: true
        },
        sharedUsage: {
            type: 'boolean',
            label: 'Shared usage',
            default: false,
            index: true
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false,
            index: true
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        },

        // Details
        detailsPayload: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Deprecation
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        purchaseDate: {
            type: 'date',
            label: 'Purchase date',
            required: false,
            index: true
        },
        originalValue: {
            type: 'decimal',
            label: 'Original value',
            default: 0
        },
        nonDepreciableValue: {
            type: 'decimal',
            label: 'Non-depreciable value',
            default: 0
        },
        accumulatedDepreciationValue: {
            type: 'decimal',
            label: 'Accumulated depreciation value',
            default: 0
        },
        depreciationValue: {
            type: 'decimal',
            label: 'Depreciation value',
            default: 0
        },
        carryingAmount: {
            type: 'decimal',
            label: 'Carrying amount',
            default: 0
        },
        deprecationMethod: {
            type: 'string',
            label: 'Deprecation method',
            default: 'linear'
        },
        deprecationPeriod: {
            type: 'string',
            label: 'Period',
            default: 'year'
        },
        deprecationDuration: {
            type: 'integer',
            label: 'Duration',
            required: false
        },
        deprecationPercentageOfDecrease: {
            type: 'decimal',
            label: 'Percentage of decrease',
            required: false
        },
        deprecationProrataTemporis: {
            type: 'boolean',
            label: 'Prorata temporis',
            default: false
        },
        deprecationProrataTemporisDate: {
            type: 'date',
            label: 'Prorata temporis date',
            required: false
        },
        deprecationCreateJournalEntriesAutomatically: {
            type: 'boolean',
            label: 'Create journal entries automatically',
            default: false
        },
        accountSetGroupId: {
            type: 'string',
            label: 'Account set group',
            required: false
        },
        fixtureAccountId: {
            type: 'string',
            label: 'Fixture account',
            required: false
        },
        deprecationAccountId: {
            type: 'string',
            label: 'Deprecation account',
            required: false
        },
        expenseAccountId: {
            type: 'string',
            label: 'Expense account',
            required: false
        },
        lossAccountId: {
            type: 'string',
            label: 'Loss account',
            required: false
        },
        profitIncomeAccountId: {
            type: 'string',
            label: 'Profit / income account',
            required: false
        },
        expenseCategoryId: {
            type: 'string',
            label: 'Expense category',
            required: false
        },
        expenseTagIds: {
            type: ['string'],
            label: 'Expense tags',
            default: []
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        isProductSelectionLocked: {
            type: 'boolean',
            default: false
        }
    },
    attributes: {
        product: {
            collection: 'inventory.products',
            parentField: 'productId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        assetType: {
            collection: 'am.asset-types',
            parentField: 'assetTypeId',
            childField: '_id'
        },
        assetCategory: {
            collection: 'am.asset-categories',
            parentField: 'assetCategoryId',
            childField: '_id'
        },
        assetClass: {
            collection: 'am.asset-classes',
            parentField: 'assetClassId',
            childField: '_id'
        },
        responsibleEmployee: {
            collection: 'kernel.partners',
            parentField: 'responsibleEmployeeId',
            childField: '_id'
        },
        department: {
            collection: 'hr.departments',
            parentField: 'departmentId',
            childField: '_id'
        },
        workingGroup: {
            collection: 'am.working-groups',
            parentField: 'workingGroupId',
            childField: '_id'
        },
        usagePurpose: {
            collection: 'am.usage-purposes',
            parentField: 'usagePurposeId',
            childField: '_id'
        },
        brandId: {
            collection: 'inventory.product-brands',
            parentField: 'brandId',
            childField: '_id'
        },
        modelId: {
            collection: 'am.models',
            parentField: 'modelId',
            childField: '_id'
        }
    }
};
