<template>
    <ui-view
        type="form"
        collection="am.fixtures"
        method="am.save-fixture"
        class="am-fixtures-detail"
        :model="model"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        full-height
        no-inner-padding
        actions="edit,cancel"
        @changed="handleChange"
        v-if="isInitialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status" />

            <ui-related-documents :documents="model.relatedDocuments" />

            <ui-workflow-approval-status
                v-if="$app.hasModule('workflow')"
                :status="model.workflowApprovalStatus"
                :definition-name="'am.fixtures'"
                :document-id="$params('id')"
                :operation="$params('id') ? 'update' : 'create'"
            />
        </template>

        <template slot="actions">
        </template>

        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="general" :label="$t('GENERAL')">
                <am-fixtures-tab-general :model="model" />
            </el-tab-pane>

            <el-tab-pane name="details" :label="$t('DETAILS')" :disabled="!model.assetTypeId">
                <am-fixtures-tab-details
                    ref="detailsForm"
                    :key="model.assetTypeId"
                    :asset-type-id="model.assetTypeId"
                    :details-payload="model.detailsPayload || {}"
                    v-if="model.assetTypeId"
                />
            </el-tab-pane>

            <el-tab-pane name="depreciation" :label="$t('DEPRECATION')">
                <am-fixtures-tab-depreciation
                    :model="model"
                    :currencies="currencies"
                    :system-currency-id="systemCurrencyId"
                />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="$t('ATTACHMENTS')">
                <ui-field name="attachments" field-type="attachments" />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import AmFixturesTabGeneral from './detail/_tab-general';
import AmFixturesTabDetails from './detail/_tab-details';
import AmFixturesTabDepreciation from './detail/_tab-depreciation';
import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        activeTab: 'general',
        extraFields: ['status', 'relatedDocuments', 'departmentPath', 'detailsPayload', 'isProductSelectionLocked', 'workflowApprovalStatus'],
        generatedCode: '',
        generatedFixtureNo: '',
        currencies: [],
        systemCurrencyId: '',
        isInitialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.name ? model.name : '';
            }

            return this.$t('New Fixture');
        },
        statuses() {
            const statuses = [{value: 'draft', label: 'Draft'}];

            if (this.status === 'in-maintenance') {
                statuses.push({value: 'in-maintenance', label: 'In Maintenance'});
            } else if (this.status === 'malfunctioning') {
                statuses.push({value: 'malfunctioning', label: 'Malfunctioning'});
            } else if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else {
                statuses.push({value: 'in-use', label: 'In Use'});
            }

            return statuses;
        },
        status() {
            const model = this.model;

            if (!model.status) {
                return 'draft';
            }

            return model.status;
        }
    },

    methods: {
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            // When creating generate code.
            if (!this.$params('id')) {
                model.code = await this.generateCode(false);
            }
            this.generatedCode = model.code;

            // When creating generate fixture no.
            if (!this.$params('id')) {
                model.fixtureNo = await this.generateFixtureNo(false);
            }
            this.generatedFixtureNo = model.fixtureNo;

            if (!model.currencyId) {
                model.currencyId = company.currencyId;
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            if (!!model.departmentId) {
                const department = await this.$collection('hr.departments').findOne({
                    _id: model.departmentId,
                    $select: ['path']
                });

                model.departmentPath = department.path;
            }

            if (this.model.code === this.generatedCode && !this.$params('id')) {
                model.code = await this.generateCode(true);
            }

            if (this.model.fixtureNo === this.generatedFixtureNo && !this.$params('id')) {
                model.fixtureNo = await this.generateFixtureNo(true);
            }

            if (!!this.$refs && !!this.$refs.detailsForm) {
                model.detailsPayload = await this.$refs.detailsForm.submitForm();
            }

            return model;
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];

            if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name']
                    });

                    if (_.isObject(currency)) {
                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.date
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }
            } else if (field === 'assetTypeId') {
                const assetType = await this.$collection('am.asset-types').findOne({
                    _id: model.assetTypeId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!assetType) {
                    this.model.accountSetGroupId = assetType.accountSetGroupId;
                    this.model.deprecationMethod = assetType.deprecationMethod;
                    this.model.deprecationPeriod = assetType.deprecationPeriod;
                    this.model.deprecationDuration = assetType.deprecationDuration || 0;
                    this.model.deprecationPercentageOfDecrease = assetType.deprecationPercentageOfDecrease || 0;
                    this.model.deprecationProrataTemporis = assetType.deprecationProrataTemporis;
                    this.model.deprecationProrataTemporisDate = assetType.deprecationProrataTemporisDate;
                    this.model.deprecationCreateJournalEntriesAutomatically =
                        assetType.deprecationCreateJournalEntriesAutomatically;
                    this.model.fixtureAccountId = assetType.fixtureAccountId;
                    this.model.deprecationAccountId = assetType.deprecationAccountId;
                    this.model.expenseAccountId = assetType.expenseAccountId;
                    this.model.lossAccountId = assetType.lossAccountId;
                    this.model.profitIncomeAccountId = assetType.profitIncomeAccountId;
                    this.model.expenseCategoryId = assetType.expenseCategoryId;
                    this.model.expenseTagIds = assetType.expenseTagIds;
                }
            } else if (field === 'accountSetGroupId') {
                const accountSetGroup = await this.$collection('kernel.account-set-groups').findOne({
                    _id: model.accountSetGroupId,
                    $disableSoftDelete: true,
                    $disableInUseCheck: true
                });

                if (!!accountSetGroup) {
                    this.model.fixtureAccountId =
                        (accountSetGroup.accounts.find(a => a.key === 'fixtureAccount') || {}).accountId || '';
                    this.model.deprecationAccountId =
                        (accountSetGroup.accounts.find(a => a.key === 'deprecationAccount') || {}).accountId || '';
                    this.model.expenseAccountId =
                        (accountSetGroup.accounts.find(a => a.key === 'expenseAccount') || {}).accountId || '';
                    this.model.lossAccountId =
                        (accountSetGroup.accounts.find(a => a.key === 'lossAccount') || {}).accountId || '';
                    this.model.profitIncomeAccountId =
                        (accountSetGroup.accounts.find(a => a.key === 'profitIncomeAccount') || {}).accountId || '';
                }
            } else if (field === 'expenseCategoryId') {
                const expenseCategory = await this.$collection('expense.expense-categories').findOne({
                    _id: model.expenseCategoryId,
                    $disableSoftDelete: true,
                    $disableInUseCheck: true
                });

                if (!!expenseCategory) {
                    this.model.expenseAccountId = expenseCategory.accountId || '';
                }
            }

            if (
                field === 'originalValue' ||
                field === 'nonDepreciableValue' ||
                field === 'accumulatedDepreciationValue'
            ) {
                this.model.depreciationValue = this.$app.round(
                    model.originalValue - model.nonDepreciableValue - model.accumulatedDepreciationValue
                );
                this.model.carryingAmount = this.$app.round(model.originalValue - model.accumulatedDepreciationValue);
            }
        },

        async generateCode(save = false) {
            const numbering = await this.$collection('kernel.numbering').findOne({
                code: 'amAssetNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            return await this.$rpc('kernel.common.request-number', {numberingId: numbering._id, save});
        },
        async generateFixtureNo(save = false) {
            const numbering = await this.$collection('kernel.numbering').findOne({
                code: 'amFixtureNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            return await this.$rpc('kernel.common.request-number', {numberingId: numbering._id, save});
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];

        this.systemCurrencyId = company.currencyId;
        this.currencies = await this.$collection('kernel.currencies').find({
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        this.isInitialized = true;

        this.$params('loading', false);
    },

    components: {
        AmFixturesTabGeneral,
        AmFixturesTabDetails,
        AmFixturesTabDepreciation
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.am-fixtures-detail {
    .ui-view-content > .el-scrollbar > .ui-form {
        padding-top: 40px;
    }
}
</style>
