import _ from 'lodash';

export default {
    name: 'fixtures',
    title: 'Fixtures',
    collection: 'am.fixtures',
    workflow: {
        allowedOperations: ['create', 'update', 'remove'],
        allowedActions: ['assignation', 'notification', 'validation'],
        documentView: 'am.fixed-assets.fixtures-detail'
    },
    documentView: 'am.fixed-assets.fixtures-detail',
     fields(app) {
        return [
        {type: 'string', name: 'code', label: 'Code'},
        {type: 'string', name: 'name', label: 'Name'},
        {type: 'string', name: 'status', label: 'Status'},
        {type: 'string', name: 'fixtureNo', label: 'Fixture no'},
        {type: 'string', name: 'barcode', label: 'Barcode'},
        {type: 'string', name: 'serialNumber', label: 'Serial number'},
        {type: 'string', name: 'acquisitionMethod', label: 'Acquisition method'},
        {type: 'date', name: 'recordDate', label: 'Record date'},
        {type: 'date', name: 'entryDate', label: 'Entry date'},
        {type: 'date', name: 'releaseDate', label: 'Release date'},
        {type: 'datetime', name: 'rentalStartDate', label: 'Rental start date'},
        {type: 'datetime', name: 'rentalEndDate', label: 'Rental end date'},
        {type: 'string', name: 'description', label: 'Description'},
        {type: 'boolean', name: 'sharedUsage', label: 'Shared usage'},
        {type: 'boolean', name: 'isActive', label: 'Is active'},
        {type: 'string', name: 'workflowApprovalStatus', label: 'Workflow approval status'},
        {type: 'string', name: 'productCode', label: 'Product code'},
        {type: 'string', name: 'productDefinition', label: 'Product definition'},
        {type: 'string', name: 'assetTypeCode', label: 'Asset type code'},
        {type: 'string', name: 'assetTypeName', label: 'Asset type name'},
        {type: 'string', name: 'assetCategoryCode', label: 'Asset category code'},
        {type: 'string', name: 'assetCategoryName', label: 'Asset category name'},
        {type: 'string', name: 'assetClassCode', label: 'Asset class code'},
        {type: 'string', name: 'assetClassName', label: 'Asset class name'},
        {type: 'string', name: 'branchCode', label: 'Branch code'},
        {type: 'string', name: 'branchName', label: 'Branch name'},
        {type: 'string', name: 'financialProjectCode', label: 'Financial project code'},
        {type: 'string', name: 'financialProjectName', label: 'Financial project name'},
        {type: 'string', name: 'responsibleEmployeeCode', label: 'Responsible employee code'},
        {type: 'string', name: 'responsibleEmployeeName', label: 'Responsible employee name'}
    ]
    },
    async bulkDocumentExtra(app, schema, documents) {
        const productIds = [];
        const assetTypeIds = [];
        const assetCategoryIds = [];
        const assetClassIds = [];
        const departmentIds = [];
        const workingGroupIds = [];
        const usagePurposeIds = [];
        const brandIds = [];
        const modelIds = [];
        const branchIds = [];
        const financialProjectIds = [];
        const responsibleEmployeeIds = [];

        for (const document of documents) {
            if (document.productId) productIds.push(document.productId);
            if (document.assetTypeId) assetTypeIds.push(document.assetTypeId);
            if (document.assetCategoryId) assetCategoryIds.push(document.assetCategoryId);
            if (document.assetClassId) assetClassIds.push(document.assetClassId);
            if (document.departmentId) departmentIds.push(document.departmentId);
            if (document.workingGroupId) workingGroupIds.push(document.workingGroupId);
            if (document.usagePurposeId) usagePurposeIds.push(document.usagePurposeId);
            if (document.brandId) brandIds.push(document.brandId);
            if (document.modelId) modelIds.push(document.modelId);
            if (document.branchId) branchIds.push(document.branchId);
            if (document.financialProjectId) financialProjectIds.push(document.financialProjectId);
            if (document.responsibleEmployeeId) responsibleEmployeeIds.push(document.responsibleEmployeeId);
        }

        const productsMap = {};
        const assetTypesMap = {};
        const assetCategoriesMap = {};
        const assetClassesMap = {};
        const departmentsMap = {};
        const workingGroupsMap = {};
        const usagePurposesMap = {};
        const brandsMap = {};
        const modelsMap = {};
        const branchesMap = {};
        const financialProjectsMap = {};
        const responsibleEmployeesMap = {};

        if (productIds.length > 0) {
            const products = await app.collection('inventory.products').find({
                _id: {$in: productIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const product of products) {
                productsMap[product._id] = product;
            }
        }

        if (assetTypeIds.length > 0) {
            const assetTypes = await app.collection('am.asset-types').find({
                _id: {$in: assetTypeIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const assetType of assetTypes) {
                assetTypesMap[assetType._id] = assetType;
            }
        }

        if (assetCategoryIds.length > 0) {
            const assetCategories = await app.collection('am.asset-categories').find({
                _id: {$in: assetCategoryIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const assetCategory of assetCategories) {
                assetCategoriesMap[assetCategory._id] = assetCategory;
            }
        }

        if (assetClassIds.length > 0) {
            const assetClasses = await app.collection('am.asset-classes').find({
                _id: {$in: assetClassIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const assetClass of assetClasses) {
                assetClassesMap[assetClass._id] = assetClass;
            }
        }

        if (branchIds.length > 0) {
            const branches = await app.collection('kernel.branches').find({
                _id: {$in: branchIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const branch of branches) {
                branchesMap[branch._id] = branch;
            }
        }

        if (financialProjectIds.length > 0) {
            const financialProjects = await app.collection('kernel.financial-projects').find({
                _id: {$in: financialProjectIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const financialProject of financialProjects) {
                financialProjectsMap[financialProject._id] = financialProject;
            }
        }

        if (responsibleEmployeeIds.length > 0) {
            const responsibleEmployees = await app.collection('kernel.partners').find({
                _id: {$in: responsibleEmployeeIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const responsibleEmployee of responsibleEmployees) {
                responsibleEmployeesMap[responsibleEmployee._id] = responsibleEmployee;
            }
        }

        return {
            productsMap,
            assetTypesMap,
            assetCategoriesMap,
            assetClassesMap,
            departmentsMap,
            workingGroupsMap,
            usagePurposesMap,
            brandsMap,
            modelsMap,
            branchesMap,
            financialProjectsMap,
            responsibleEmployeesMap
        };
    },
    async data(app, schema, document, {
        productsMap = {},
        assetTypesMap = {},
        assetCategoriesMap = {},
        assetClassesMap = {},
        branchesMap = {},
        financialProjectsMap = {},
        responsibleEmployeesMap = {}
    }) {
        const data = {};

        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'in-use', label: 'In Use'},
            {value: 'in-maintenance', label: 'In Maintenance'},
            {value: 'malfunctioning', label: 'Malfunctioning'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const statusLabel = (statusOptions.find(option => option.value === document.status) || {}).label || '';
        if (!!statusLabel) {
            data.status = app.translate(statusLabel);
        } else {
            data.status = app.translate('Draft');
        }

        data.code = document.code;
        data.name = document.name;
        data.fixtureNo = document.fixtureNo;
        data.barcode = document.barcode;
        data.serialNumber = document.serialNumber;
        data.acquisitionMethod = document.acquisitionMethod;
        data.recordDate = document.recordDate;
        data.entryDate = document.entryDate;
        data.releaseDate = document.releaseDate;
        data.rentalStartDate = document.rentalStartDate;
        data.rentalEndDate = document.rentalEndDate;
        data.description = document.description;
        data.sharedUsage = document.sharedUsage;
        data.isActive = document.isActive;
        data.workflowApprovalStatus = document.workflowApprovalStatus;

        if (document.productId) {
            const product = productsMap[document.productId];
            if (product) {
                data.productCode = product.code;
                data.productDefinition = product.name;
            }
        }

        if (document.assetTypeId) {
            const assetType = assetTypesMap[document.assetTypeId];
            if (assetType) {
                data.assetTypeCode = assetType.code;
                data.assetTypeName = assetType.name;
            }
        }

        if (document.assetCategoryId) {
            const assetCategory = assetCategoriesMap[document.assetCategoryId];
            if (assetCategory) {
                data.assetCategoryCode = assetCategory.code;
                data.assetCategoryName = assetCategory.name;
            }
        }

        if (document.assetClassId) {
            const assetClass = assetClassesMap[document.assetClassId];
            if (assetClass) {
                data.assetClassCode = assetClass.code;
                data.assetClassName = assetClass.name;
            }
        }

        if (document.branchId) {
            const branch = branchesMap[document.branchId];
            if (branch) {
                data.branchCode = branch.code;
                data.branchName = branch.name;
            }
        }

        if (document.financialProjectId) {
            const financialProject = financialProjectsMap[document.financialProjectId];
            if (financialProject) {
                data.financialProjectCode = financialProject.code;
                data.financialProjectName = financialProject.name;
            }
        }

        if (document.responsibleEmployeeId) {
            const responsibleEmployee = responsibleEmployeesMap[document.responsibleEmployeeId];
            if (responsibleEmployee) {
                data.responsibleEmployeeCode = responsibleEmployee.code;
                data.responsibleEmployeeName = responsibleEmployee.name;
            }
        }

        return data;
    }
};
