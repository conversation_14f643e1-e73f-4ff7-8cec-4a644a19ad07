import _ from 'lodash';

export default {
    name: 'save-fixture',
    async action({data, id}, params) {
        const app = this.app;
        const isCreate = !id;
        const collection = app.collection('am.fixtures');

        let fixture = _.cloneDeep(data);
        if (!!id) {
            fixture = {...(await collection.get(id)), ...fixture};
        }

        if (!!fixture.productId) {
            const product = await app.collection('inventory.products').findOne({
                _id: fixture.productId,
                $select: ['code', 'definition'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.productCode = product.code;
            data.productDefinition = product.definition;
        }
        if (!!fixture.assetTypeId) {
            const assetType = await app.collection('am.asset-types').findOne({
                _id: fixture.assetTypeId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.assetTypeCode = assetType.code;
            data.assetTypeName = assetType.name;
        }
        if (!!fixture.assetCategoryId) {
            const assetCategory = await app.collection('am.asset-categories').findOne({
                _id: fixture.assetCategoryId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.assetCategoryCode = assetCategory.code;
            data.assetCategoryName = assetCategory.name;
        }
        if (!!fixture.assetClassId) {
            const assetClass = await app.collection('am.asset-classes').findOne({
                _id: fixture.assetClassId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.assetClassCode = assetClass.code;
            data.assetClassName = assetClass.name;
        }
        if (!!fixture.departmentId) {
            const department = await app.collection('hr.departments').findOne({
                _id: fixture.departmentId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.departmentCode = department.code;
            data.departmentName = department.name;
        }
        if (!!fixture.workingGroupId) {
            const workingGroup = await app.collection('am.working-groups').findOne({
                _id: fixture.workingGroupId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.workingGroupCode = workingGroup.code;
            data.workingGroupName = workingGroup.name;
        }
        if (!!fixture.usagePurposeId) {
            const usagePurpose = await app.collection('am.usage-purposes').findOne({
                _id: fixture.usagePurposeId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.usagePurposeCode = usagePurpose.code;
            data.usagePurposeName = usagePurpose.name;
        }
        if (!!fixture.brandId) {
            const brand = await app.collection('inventory.product-brands').findOne({
                _id: fixture.brandId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.brandCode = brand.code;
            data.brandName = brand.name;
        }
        if (!!fixture.modelId) {
            const model = await app.collection('am.models').findOne({
                _id: fixture.modelId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.modelCode = model.code;
            data.modelName = model.name;
        }
        if (!!fixture.branchId) {
            const branch = await app.collection('kernel.branches').findOne({
                _id: fixture.branchId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.branchCode = branch.code;
            data.branchName = branch.name;
        }
        if (!!fixture.financialProjectId) {
            const project = await app.collection('kernel.financial-projects').findOne({
                _id: fixture.financialProjectId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.projectCode = project.code;
            data.projectName = project.name;
        }
        if (!!fixture.responsibleEmployeeId) {
            const responsibleEmployee = await app.collection('kernel.partners').findOne({
                _id: fixture.responsibleEmployeeId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            data.responsibleEmployeeCode = responsibleEmployee.code;
            data.responsibleEmployeeName = responsibleEmployee.name;
        } else {
            data.responsibleEmployeeCode = '';
            data.responsibleEmployeeName = '';
        }

        if (!isCreate) {
            if (_.isString(data.bulkOperationError) && !_.isEmpty(data.bulkOperationError)) {
                data.bulkOperationError = '';
            }

            if (app.hasModule('workflow')) {
                const workflowData = {
                    ...data
                };

                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'am.fixtures',
                        data: workflowData,
                        id,
                        operation: 'update',
                        actionTypes: ['assignation', 'validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            const result = await collection.patch({_id: id}, data, {
                user: params.user,
                checkPermission: true
            });

            if (app.hasModule('workflow')) {
                const workflowData = {
                    ...result
                };

                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'am.fixtures',
                        data: workflowData,
                        id,
                        operation: 'update',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            return result;
        } else {
            if (app.hasModule('workflow')) {
                const workflowData = {
                    ...data
                };

                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'am.fixtures',
                        data: workflowData,
                        operation: 'create',
                        actionTypes: ['validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            const result = await collection.create(data, {
                user: params.user,
                checkPermission: true
            });

            if (app.hasModule('workflow')) {
                const workflowData = {
                    ...result
                };

                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'am.fixtures',
                        data: workflowData,
                        operation: 'create',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            return result;
        }
    }
};
