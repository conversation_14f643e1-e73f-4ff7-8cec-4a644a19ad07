<template>
    <div class="studio-toolbar-actions">
        <el-dropdown
            @command="handleAction"
            trigger="click"
            placement="bottom-end"
            @visible-change="isVisible => (isActionsVisible = isVisible)"
        >
            <el-button :class="{'is-actions-visible': isActionsVisible}" size="small" icon="fal fa-ellipsis-h" plain />
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="download-as-pdf" icon="fal fa-file-pdf">
                    {{ $t('Download as pdf') }}
                </el-dropdown-item>
                <el-dropdown-item command="download-as-image" icon="fal fa-file-image">
                    {{ $t('Download as image') }}
                </el-dropdown-item>
                <el-dropdown-item command="dashboard-settings" icon="fal fa-cog" v-if="canEdit">
                    {{ $t('Dashboard settings') }}
                </el-dropdown-item>
                <el-dropdown-item
                    command="delete-dashboard"
                    icon="fal fa-trash-alt"
                    class="text-danger"
                    v-if="canRemove"
                >
                    {{ $t('Delete dashboard') }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script>
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import saveAs from 'framework/save-as';
import store from '../../store';

export default {
    data: () => ({
        isActionsVisible: false
    }),

    computed: {
        canEdit() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'update'
            });
        },
        canRemove() {
            return this.$app.hasPermission({
                type: 'record',
                name: 'bi.dashboards',
                method: 'remove'
            });
        },
        dashboard() {
            return store.state.dashboard;
        }
    },

    methods: {
        async handleAction(action) {
            if (action === 'download-as-image') {
                await this.handleDownloadAsImage();
            } else if (action === 'download-as-pdf') {
                await this.handleDownloadAsPdf();
            } else if (action === 'dashboard-settings') {
                this.handleDashboardSettings();
            } else if (action === 'delete-dashboard') {
                this.handleDeleteDashboard();
            }
        },

        async handleDownloadAsImage() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }

                dashboardEl.classList.add('is-exporting');

                const canvas = await html2canvas(dashboardEl, {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true,
                    allowTaint: true
                });

                dashboardEl.classList.remove('is-exporting');

                canvas.toBlob(blob => {
                    const fileName = this.dashboard?.title || 'dashboard';
                    saveAs(blob, `${fileName}.png`);

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                });
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        async handleDownloadAsPdf() {
            this.$params('loading', true);

            try {
                const dashboardEl = document.querySelector('.bi-dashboard-studio');
                if (!dashboardEl) {
                    throw new Error('Dashboard element not found');
                }

                dashboardEl.classList.add('is-exporting');

                const canvas = await html2canvas(dashboardEl, {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true,
                    allowTaint: true
                });

                dashboardEl.classList.remove('is-exporting');

                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF({
                    orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
                    unit: 'px',
                    format: [canvas.width, canvas.height]
                });

                pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);

                const fileName = this.dashboard?.title || 'dashboard';
                pdf.save(`${fileName}.pdf`);

                this.$params('loading', false);
            } catch (error) {
                this.$program.message('error', error.message);
                this.$params('loading', false);
            }
        },

        handleDashboardSettings() {
            // TODO: Implement dashboard settings functionality
            this.$program.message('info', 'Dashboard settings functionality will be implemented');
        },

        handleDeleteDashboard() {
            this.$program.alert('confirm', this.$t('Are you sure you want to delete this dashboard?'), async confirmed => {
                if (!confirmed) return;

                try {
                    this.$params('loading', true);
                    await this.$collection('bi.dashboards').remove({_id: this.dashboard._id});

                    // Navigate back to dashboard list
                    this.$parent.$parent.$emit('canceled');

                    this.$params('loading', false);
                } catch (error) {
                    this.$program.message('error', error.message);
                    this.$params('loading', false);
                }
            });
        }
    }
};
</script>
