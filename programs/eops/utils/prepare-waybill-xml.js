import _ from 'lodash';
import xml2js from 'xml2js';
import {v4 as uuid} from 'uuid';
import {toUpper} from 'framework/helpers';
import QRCode from 'qrcode';
import waybill from '../../inventory/templates/waybill';

export default async function prepareWaybillXML(app, {eWaybillId, waybillDocumentNo, issueDate, waybillTemplateId}) {
    // General.
    const xmlBuilder = new xml2js.Builder({
        explicitRoot: false,
        xmldec: {version: '1.0', encoding: 'UTF-8'},
        cdata: true
    });
    const waybillUUID = uuid();
    const round = app.roundNumber;
    const useOutputPrecision = app.setting('eops.useOutputPrecisionsOnEWaybills');
    const currencyPrecision = useOutputPrecision
        ? app.setting('system.outputCurrencyPrecision') || app.setting('system.currencyPrecision')
        : app.setting('system.currencyPrecision');
    const eWaybill = await app.collection('logistics.logistics').get(eWaybillId);
    const isReturnWaybill = !!eWaybill.isReturnWaybill;
    const company = {
        ...(await app.collection('kernel.company').findOne({})),
        isCompany: true
    };

    // Units.
    const units = await app.collection('kernel.units').find({
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const unitsMap = {};
    for (const unit of units) {
        unitsMap[unit._id] = unit;
    }

    // Product map.
    const products = await app.collection('inventory.products').find({
        _id: {$in: _.uniq(eWaybill.items.map(item => item.productId))},
        $select: ['code', 'name', 'definition', 'baseUnitId', 'brandId', 'unitRatios', 'unitMeasurements'],
        $populate: [{field: 'brand'}],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const productsMap = {};
    for (const product of products) {
        productsMap[product._id] = product;
    }

    // Evaluate multi company.
    if (!!app.setting('system.multiCompany')) {
        const branch = await app.collection('kernel.branches').findOne({
            _id: eWaybill.branchId,
            $select: ['companyId'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const subCompanies = company.subCompanies || [];
        const subCompany = subCompanies.find(subCompany => subCompany.id === branch.companyId);
        if (!subCompany) {
            throw new Error('Sub-company is not found!');
        }

        company.name = subCompany.name;
        company.tagline = subCompany.tagline;
        company.email = subCompany.email;
        company.website = subCompany.website;
        company.legalName = subCompany.legalName;
        company.tin = subCompany.tin;
        company.taxDepartment = subCompany.taxDepartment;
        company.firstName = subCompany.firstName;
        company.lastName = subCompany.lastName;
        company.identity = subCompany.identity;
        company.mersisNo = subCompany.mersisNo;
        company.ticaretSicilNo = subCompany.ticaretSicilNo;
        company.phone = subCompany.phone;
        company.phoneCountryCode = subCompany.phoneCountryCode;
        company.phoneNumbers = subCompany.phoneNumbers;
        company.address = subCompany.address;
    }

    const partner = !!eWaybill.partnerId ? await app.collection('kernel.partners').get(eWaybill.partnerId) : {};
    const currency = await app.collection('kernel.currencies').findOne({
        _id: eWaybill.currencyId,
        $select: ['name'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const currencyName = currency.name === 'TL' ? 'TRY' : currency.name;
    const companyCurrencyName = company.currency.name === 'TL' ? 'TRY' : company.currency.name;
    const dontSendUnitPrices = app.setting('eops.eWaybillDontSendUnitPrices');
    const mersisNo = !!company.mersisNo ? company.mersisNo : app.setting('eops.mersisNo');
    const ticaretSicilNo = !!company.ticaretSicilNo ? company.ticaretSicilNo : app.setting('eops.ticaretSicilNo');

    // Evaluate multi branch.
    if (!!app.setting('system.multiBranch')) {
        const branch = await app.collection('kernel.branches').findOne({
            _id: eWaybill.branchId,
            $select: ['name', 'address'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (!!branch) {
            company.name = `${company.name} - ${branch.name}`;
            company.legalName = `${company.legalName} - ${branch.name}`;
            company.address = branch.address;
        }
    }

    // Notes.
    const notes = [];
    if (!!eWaybill.note) {
        notes.push(eWaybill.note);
    }
    if (!!eWaybill.financialProjectName) {
        notes.push(`Proje adı: ${eWaybill.financialProjectName}`);
    }

       console.log('eWaybill.currencyId', eWaybill);
    console.log('company.currencyId', company.currencyId);
    console.log('ewaybill.currencyId', eWaybill.currencyRate);

   if (eWaybill.currencyId !== company.currencyId) {
        notes.push(`${currencyName}: ${eWaybill.currencyRate}`);
    }
    console.log('notes', notes);

    //AdditionalInformation
    let partnerAdditionalInfo = null;
    if (!!partner.additionalInformation) {
        if (typeof partner.additionalInformation === 'object') {
            const additionalInfoFields = [];
            for (const [key,value] of Object.entries(partner.additionalInformation)) {
                if (value !== null && value !== undefined) {
                    additionalInfoFields.push(`${value}`);
                }
            }
            if (additionalInfoFields.length > 0) {
                partnerAdditionalInfo = additionalInfoFields.join(', ');
            }
        } else {
            partnerAdditionalInfo = partner.additionalInformation;
        }
    }

    if (!!eWaybill.shippingPaymentType) {
        const shippingPaymentTypeOptions = [
            {value: 'freight-prepaid', label: 'Gönderici Ödemeli'},
            {value: 'freight-collect', label: 'Alıcı Ödemeli'}
        ];

        const option = shippingPaymentTypeOptions.find(option => option.value === eWaybill.shippingPaymentType);
        if (option) {
            notes.push(`Teslimat ödeme şekli: ${option.label}`);
        }
    }
    let netWeight = 0;
    let grossWeight = 0;
    for (const item of eWaybill.items || []) {
        const product = productsMap[item.productId];
        if (!product) {
            continue;
        }
        const um = (product.unitMeasurements || []).find(m => m.unitId === item.unitId);
        if (!um) {
            continue;
        }
        const weightUnit = units.find(u => u.category === 'weight' && u.symbol === 'kg');
        if (!weightUnit) {
            continue;
        }

        let itemNetWeight = 0;
        let itemGrossWeight = 0;
        let netWeightUnitId = null;
        let grossWeightUnitId = null;

        if (_.isFinite(um.netWeight) && um.netWeightUnitId) {
            itemNetWeight = um.netWeight * (item.deliveredQty ?? 0);
            netWeightUnitId = um.netWeightUnitId;
        }
        if (_.isFinite(um.grossWeight) && um.grossWeightUnitId) {
            itemGrossWeight = um.grossWeight * (item.deliveredQty ?? 0);
            grossWeightUnitId = um.grossWeightUnitId;
        }

        const netWeightUnit = unitsMap[netWeightUnitId];
        if (!!netWeightUnit) {
            if (netWeightUnit.type === 'smaller') netWeight = netWeight / netWeightUnit.ratio;
            else if (netWeightUnit.type === 'bigger') netWeight = netWeight * netWeightUnit.ratio;
        }
        const grossWeightUnit = unitsMap[grossWeightUnitId];
        if (!!grossWeightUnit) {
            if (grossWeightUnit.type === 'smaller') grossWeight = grossWeight / grossWeightUnit.ratio;
            else if (grossWeightUnit.type === 'bigger') grossWeight = grossWeight * grossWeightUnit.ratio;
        }

        netWeight += itemNetWeight;
        grossWeight += itemGrossWeight;
    }
    if (netWeight > 0) {
        notes.push(`NET AĞIRLIK: ${app.format(netWeight, 'amount')}`);
    }
    if (grossWeight > 0) {
        notes.push(`BRÜT AĞIRLIK: ${app.format(grossWeight, 'amount')}`);
    }


    const deliveryAddress = eWaybill.deliveryAddress;

    // Update partner.
    partner.isCompany = !!eWaybill.partnerIsCompany;
    partner.code = eWaybill.partnerCode;
    partner.email = eWaybill.partnerEmail;
    partner.phone = eWaybill.partnerPhone;
    if (eWaybill.partnerIsCompany) {
        partner.legalName = eWaybill.partnerName;
        partner.tin = eWaybill.partnerTinIdentity;
        partner.taxDeppartment = eWaybill.partnerTaxDepartment;
    } else {
        const nameParts = eWaybill.partnerName.split(' ');

        partner.name = eWaybill.partnerName;
        partner.lastName = nameParts.pop();
        partner.firstName = nameParts.join(' ');
        partner.identity = eWaybill.partnerTinIdentity;
    }
    if (partner.tin === '2222222222') {
        partner.tin = '3900892152';
    }
    if (!partner.address) {
        partner.address = deliveryAddress;
    }

    // Determine parties.
    const supplier = company;
    const customer = !!isReturnWaybill
        ? {
              ...company,
              isCompany: true
          }
        : partner;

    // Get contact.
    let contact = null;
    if (!!eWaybill.contactPersonId) {
        contact = await app.collection('kernel.contacts').findOne({
            _id: eWaybill.contactPersonId,
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
    }

    // Get carrier.
    let carrier = null;
    if (eWaybill.carrierId) {
        carrier = await app.collection('logistics.carriers').findOne({
            _id: eWaybill.carrierId,
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        carrier.address = eWaybill.carrierAddress;
    }

    // Get warehouse.
    let warehouse = null;
    if (!!eWaybill.warehouseId) {
        warehouse = await app.collection('inventory.warehouses').findOne({
            _id: eWaybill.warehouseId,
            $select: ['shortName', 'name', 'address'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        if (!!eWaybill.warehouseAddress) {
            warehouse.address = eWaybill.warehouseAddress;
        }
    }

    // Get order reference.
    const transfer = await app.collection('inventory.transfers').findOne({
        _id: eWaybill.transferId,
        $select: ['referenceCollection', 'referenceId', 'deliveryAddressId'],
        $disableSoftDelete: true
    });
    let orderReference = null;
    if (!!transfer && !!transfer.referenceCollection && !!transfer.referenceId) {
        const document = await app.collection(transfer.referenceCollection).findOne({
            _id: transfer.referenceId,
            $select: ['code', 'recordDate', 'issueDate', 'orderDate']
        });

        let issueDate = document.recordDate;
        if (_.isDate(document.issueDate)) {
            issueDate = document.issueDate;
        } else if (_.isDate(document.orderDate)) {
            issueDate = document.orderDate;
        }

        orderReference = {
            code: document.code,
            issueDate
        };
    }
    if (app.setting('eops.showDeliveryAddressCodeInNotesOnEWaybills') && transfer && transfer.deliveryAddressId) {
        const contact = await app.collection('kernel.contacts').findOne({
            _id: transfer.deliveryAddressId,
            $select: ['code'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        if (contact && contact.code) {
            notes.push(`Teslimat adresi kodu: ${contact.code}`);
        }
    }

    // Check document no.
    if (waybillDocumentNo.length !== 16) {
        throw new Error('Invalid document no!');
    }

    // Unit codes.
    const unitCodes = {};
    for (const uc of app.setting('eops.unitCodes') || []) {
        if (!!unitCodes[uc.unitId]) {
            continue;
        }

        unitCodes[uc.unitId] = uc.code;

        if (_.isString(unitCodes[uc.unitId])) {
            unitCodes[uc.unitId] = unitCodes[uc.unitId].trim();
        }
    }

    // Country codes.
    const countries = await app.collection('kernel.countries').find({});
    const countryNames = {};
    for (const country of countries) {
        countryNames[country._id] = toUpper(country.name);

        if (_.isString(countryNames[country._id])) {
            countryNames[country._id] = countryNames[country._id].trim();
        }
    }

    // Check if we have all the required unit codes.
    for (const item of eWaybill.items) {
        if (!unitCodes[item.unitId]) {
            const unit = await app.collection('kernel.units').findOne({
                _id: item.unitId,
                $select: ['name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            throw new app.errors.Unprocessable(
                app.translate('Could not found the unit code for {{unit}}!', {
                    unit: unit.name
                })
            );
        }
    }

    // Check supplier address.
    if (
        !supplier.address.city ||
        supplier.address.city === '' ||
        !supplier.address.district ||
        supplier.address.district === '' ||
        !supplier.address.subDistrict ||
        supplier.address.subDistrict === '' ||
        !supplier.address.street ||
        supplier.address.street === ''
    ) {
        throw new app.errors.Unprocessable(app.translate('Supplier address is invalid!'));
    }

    // Check customer.
    if (
        (customer.isCompany &&
            (!customer.legalName || customer.legalName === '' || !customer.tin || customer.tin === '')) ||
        (!customer.isCompany &&
            (!customer.name || customer.name === '' || !customer.identity || customer.identity === ''))
    ) {
        throw new app.errors.Unprocessable(app.translate('Customer is invalid!'));
    }

    // Check customer address.
    if (
        !customer.address.city ||
        customer.address.city === '' ||
        !customer.address.district ||
        customer.address.district === '' ||
        !customer.address.subDistrict ||
        customer.address.subDistrict === '' ||
        !customer.address.street ||
        customer.address.street === ''
    ) {
        throw new app.errors.Unprocessable(app.translate('Customer address is invalid!'));
    }

    // Check delivery address.
    if (
        !deliveryAddress.city ||
        deliveryAddress.city === '' ||
        !deliveryAddress.district ||
        deliveryAddress.district === '' ||
        !deliveryAddress.subDistrict ||
        deliveryAddress.subDistrict === '' ||
        !deliveryAddress.street ||
        deliveryAddress.street === ''
    ) {
        throw new app.errors.Unprocessable(app.translate('Delivery address is invalid!'));
    }

    // Waybill design.
    let design = null;
    if (!!app.setting('eops.useSystemDesignsForEWaybills')) {
        const template = !!waybillTemplateId
            ? await app.collection('eops.templates').findOne({
                  _id: waybillTemplateId
              })
            : await app.collection('eops.templates').findOne({
                  type: 'e-waybill'
              });

        if (!!template) {
            const content = template.content.replace(
                '{qr}',
                await QRCode.toDataURL(waybillUUID, {
                    type: 'image/jpeg'
                })
            );

            design = Buffer.from(content).toString('base64');
        }
    }

    // Waybill xml object.
    const waybillObj = {
        DespatchAdvice: {
            $: {
                'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
                'xsi:schemaLocation':
                    'urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2 ../xsdrt/maindoc/UBL-DespatchAdvice-2.1.xsd',
                xmlns: 'urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2',
                'xmlns:ext': 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2',
                'xmlns:cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
                'xmlns:cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2'
            }
        }
    };
    // Waybill general.
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cbc:UBLVersionID': '2.1',
        'cbc:CustomizationID': 'TR1.2',
        'cbc:ProfileID': 'TEMELIRSALIYE',
        'cbc:ID': waybillDocumentNo,
        'cbc:CopyIndicator': false,
        'cbc:UUID': waybillUUID,
        'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
        'cbc:IssueTime': app.datetime.fromJSDate(issueDate).toFormat('HH:mm:ss'),
        'cbc:DespatchAdviceTypeCode': 'SEVK',
        'cbc:AdditionalInformation': partnerAdditionalInfo,
        'cbc:Note': notes,
        ...(!!eWaybill.partnerOrderReference && _.isDate(eWaybill.partnerOrderDate)
            ? {
                  'cac:OrderReference': {
                      'cbc:ID': eWaybill.partnerOrderReference,
                      'cbc:IssueDate': app.datetime
                          .fromJSDate(eWaybill.partnerOrderDate)
                          .startOf('day')
                          .toFormat('yyyy-LL-dd')
                  }
              }
            : !!orderReference && _.isDate(orderReference.issueDate)
            ? {
                  'cac:OrderReference': {
                      'cbc:ID': orderReference.code,
                      'cbc:IssueDate': app.datetime
                          .fromJSDate(orderReference.issueDate)
                          .startOf('day')
                          .toFormat('yyyy-LL-dd')
                  }
              }
            : {}),
        'cac:AdditionalDocumentReference': [
            {
                'cbc:ID': eWaybill.code,
                'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
                'cbc:DocumentTypeCode': 'CUST_DES_ID'
            },
            ...(!!design
                ? [
                      {
                          'cbc:ID': '1',
                          'cbc:IssueDate': app.datetime.fromJSDate(issueDate).startOf('day').toFormat('yyyy-LL-dd'),
                          'cbc:DocumentTypeCode': 'XSLT',
                          'cbc:DocumentType': 'XSLT',
                          'cac:Attachment': {
                              'cbc:EmbeddedDocumentBinaryObject': {
                                  $: {
                                      characterSetCode: 'UTF-8',
                                      encodingCode: 'Base64',
                                      filename: `${waybillDocumentNo}.xslt`,
                                      mimeCode: 'application/xml'
                                  },
                                  _: design
                              }
                          }
                      }
                  ]
                : [])
        ]
    };

    // Signature
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cac:Signature': {
            'cbc:ID': {
                $: {
                    schemeID: 'VKN_TCKN'
                },
                _: company.tin
            },
            'cac:SignatoryParty': {
                'cbc:WebsiteURI': company.website,
                'cac:PartyIdentification': {
                    'cbc:ID': {
                        $: {
                            schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                        },
                        _: company.tin
                    }
                },
                'cac:PartyName': {
                    'cbc:Name': company.legalName
                },
                'cac:PostalAddress': {
                    'cbc:Room': company.address.doorNumber,
                    'cbc:StreetName': company.address.street,
                    'cbc:BuildingNumber': company.address.apartmentNumber,
                    'cbc:CitySubdivisionName': company.address.district,
                    'cbc:CityName': company.address.city,
                    'cbc:PostalZone': company.address.postalCode,
                    'cac:Country': {
                        'cbc:Name': countryNames[company.address.countryId]
                    }
                },
                'cac:Contact': {
                    'cbc:Telephone': company.phone,
                    'cbc:ElectronicMail': company.email
                },
                ...((company.tin || '').length === 11
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': company.firstName,
                              'cbc:FamilyName': company.lastName
                          }
                      }
                    : {})
            },
            'cac:DigitalSignatureAttachment': {
                'cac:ExternalReference': {
                    'cbc:URI': '#Signature'
                }
            }
        }
    };

    // Supplier.
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cac:DespatchSupplierParty': {
            'cac:Party': {
                'cbc:WebsiteURI': supplier.website,
                ...((_.isString(mersisNo) && mersisNo.length > 0) ||
                (_.isString(ticaretSicilNo) && ticaretSicilNo.length > 0)
                    ? {
                          'cac:PartyIdentification': [
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                                      },
                                      _: company.tin
                                  }
                              },
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: 'MERSISNO'
                                      },
                                      _: !!mersisNo ? mersisNo : ''
                                  }
                              },
                              {
                                  'cbc:ID': {
                                      $: {
                                          schemeID: 'TICARETSICILNO'
                                      },
                                      _: !!ticaretSicilNo ? ticaretSicilNo : ''
                                  }
                              }
                          ]
                      }
                    : {
                          'cac:PartyIdentification': {
                              'cbc:ID': {
                                  $: {
                                      schemeID: (company.tin || '').length === 11 ? 'TCKN' : 'VKN'
                                  },
                                  _: company.tin
                              }
                          }
                      }),
                'cac:PartyName': {
                    'cbc:Name': supplier.legalName
                },
                'cac:PostalAddress': {
                    'cbc:Room': supplier.address.doorNumber,
                    'cbc:StreetName': `${supplier.address.subDistrict} ${supplier.address.street}`,
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': supplier.address.apartmentNumber,
                    'cbc:CitySubdivisionName': supplier.address.district,
                    'cbc:CityName': supplier.address.city,
                    'cbc:PostalZone': supplier.address.postalCode,
                    'cbc:Region': '',
                    // 'cbc:District': supplier.address.subDistrict,
                    'cac:Country': {
                        'cbc:Name': countryNames[supplier.address.countryId]
                    }
                },
                ...(!!warehouse && !!warehouse.address
                    ? {
                          'cac:PostalAddress': {
                              'cbc:Room': warehouse.address.doorNumber,
                              'cbc:StreetName': `${warehouse.address.subDistrict} ${warehouse.address.street}`,
                              'cbc:BuildingName': '',
                              'cbc:BuildingNumber': warehouse.address.apartmentNumber,
                              'cbc:CitySubdivisionName': warehouse.address.district,
                              'cbc:CityName': warehouse.address.city,
                              'cbc:PostalZone': warehouse.address.postalCode,
                              'cbc:Region': '',
                               // 'cbc:District': warehouse.address.subDistrict,
                              'cac:Country': {
                                  'cbc:Name': countryNames[warehouse.address.countryId]
                              }
                          },
                          'cac:PhysicalLocation': {
                              'cbc:ID': `${warehouse.shortName} - ${warehouse.name}`,
                              'cac:Address': {
                                  'cbc:Room': warehouse.address.doorNumber,
                                  'cbc:StreetName': `${warehouse.address.subDistrict} ${warehouse.address.street}`,
                                  'cbc:BuildingName': '',
                                  'cbc:BuildingNumber': warehouse.address.apartmentNumber,
                                  'cbc:CitySubdivisionName': warehouse.address.district,
                                  'cbc:CityName': warehouse.address.city,
                                  'cbc:PostalZone': warehouse.address.postalCode,
                                  'cbc:Region': '',
                                  // 'cbc:District': supplier.address.subDistrict,
                                  'cac:Country': {
                                      'cbc:Name': countryNames[warehouse.address.countryId]
                                  }
                              }
                          }
                      }
                    : {}),
                ...(!!supplier.taxDepartment
                    ? {
                          'cac:PartyTaxScheme': {
                              'cac:TaxScheme': {
                                  'cbc:Name': supplier.taxDepartment || '',
                                  'cbc:TaxTypeCode': '0'
                              }
                          }
                      }
                    : {}),
                'cac:Contact': {
                    'cbc:Telephone': supplier.phone,
                    'cbc:Telefax': '',
                    'cbc:ElectronicMail': supplier.email
                },
                ...((company.tin || '').length === 11
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': company.firstName,
                              'cbc:FamilyName': company.lastName
                          }
                      }
                    : {})
            },
            ...(!!contact
                ? {
                      'cac:DespatchContact': {
                          'cbc:Name': contact.name,
                          'cbc:Telephone': contact.phone,
                          'cbc:Telefax': '',
                          'cbc:ElectronicMail': contact.email
                      }
                  }
                : {})
        }
    };

    // Customer.
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cac:DeliveryCustomerParty': {
            'cac:Party': {
                'cbc:WebsiteURI': customer.website,
                ...(customer.isCompany
                    ? {
                          'cac:PartyIdentification': !!eWaybill.deliveryAddressCode
                              ? [
                                    {
                                        'cbc:ID': {
                                            $: {
                                                schemeID: 'VKN'
                                            },
                                            _: customer.tin
                                        }
                                    },
                                    {
                                        'cbc:ID': {
                                            $: {
                                                schemeID: 'BAYINO'
                                            },
                                            _: eWaybill.deliveryAddressCode
                                        }
                                    }
                                ]
                              : {
                                    'cbc:ID': {
                                        $: {
                                            schemeID: 'VKN'
                                        },
                                        _: customer.tin
                                    }
                                },
                          'cac:PartyName': {
                              'cbc:Name': customer.legalName
                          }
                      }
                    : {
                          'cac:PartyIdentification': !!eWaybill.deliveryAddressCode
                              ? [
                                    {
                                        'cbc:ID': {
                                            $: {
                                                schemeID: 'TCKN'
                                            },
                                            _: customer.identity
                                        }
                                    },
                                    {
                                        'cbc:ID': {
                                            $: {
                                                schemeID: 'BAYINO'
                                            },
                                            _: eWaybill.deliveryAddressCode
                                        }
                                    }
                                ]
                              : {
                                    'cbc:ID': {
                                        $: {
                                            schemeID: 'TCKN'
                                        },
                                        _: customer.identity
                                    }
                                }
                          // 'cac:PartyName': {
                          //     'cbc:Name': customer.name
                          // }
                      }),
                'cac:PostalAddress': {
                    'cbc:Room': customer.address.doorNumber,
                    'cbc:StreetName': `${customer.address.subDistrict} ${customer.address.street}`,
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': customer.address.apartmentNumber,
                    'cbc:CitySubdivisionName': customer.address.district,
                    'cbc:CityName': customer.address.city,
                    'cbc:PostalZone': customer.address.postalCode,
                    'cbc:Region': '',
                    // 'cbc:District': customer.address.subDistrict,
                    'cac:Country': {
                        'cbc:Name': countryNames[customer.address.countryId]
                    }
                },
                ...(customer.isCompany
                    ? {
                          ...(!!customer.taxDepartment
                              ? {
                                    'cac:PartyTaxScheme': {
                                        'cac:TaxScheme': {
                                            'cbc:Name': customer.taxDepartment || '',
                                            'cbc:TaxTypeCode': '0'
                                        }
                                    }
                                }
                              : {})
                      }
                    : {}),
                'cac:Contact': {
                    'cbc:Telephone': customer.phone,
                    'cbc:Telefax': '',
                    'cbc:ElectronicMail': customer.email
                },
                ...(!customer.isCompany
                    ? {
                          'cac:Person': {
                              'cbc:FirstName': customer.firstName,
                              'cbc:FamilyName': customer.lastName,
                              'cbc:MiddleName': ''
                          }
                      }
                    : {})
            }
        }
    };

    // Shipment.
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cac:Shipment': {
            'cbc:ID': !!eWaybill.trackingCode ? eWaybill.trackingCode : '1',
            'cac:GoodsItem': {
                'cbc:ValueAmount': {
                    $: {
                        currencyID: currencyName
                    },
                    _: !dontSendUnitPrices ? round(eWaybill.total, currencyPrecision) : 0
                }
            },
            'cac:ShipmentStage': {
                ...(!!eWaybill.drivers && eWaybill.drivers.length > 0
                    ? {
                          'cac:TransportMeans': {
                              'cac:RoadTransport': {
                                  'cbc:LicensePlateID': eWaybill.drivers.map(driver => ({
                                      $: {
                                          schemeID: 'PLAKA'
                                      },
                                      _: driver.licensePlate
                                  }))
                              }
                          },
                          'cac:DriverPerson': eWaybill.drivers.map(driver => ({
                              'cbc:FirstName': driver.firstName,
                              'cbc:FamilyName': driver.lastName,
                              'cbc:Title': 'Şoför',
                              'cbc:NationalityID': driver.identity
                          }))
                      }
                    : {})
            },
            'cac:Delivery': {
                'cac:DeliveryAddress': {
                    'cbc:Room': deliveryAddress.doorNumber,
                    'cbc:StreetName': `${deliveryAddress.subDistrict} ${deliveryAddress.street}`,
                    'cbc:BuildingName': '',
                    'cbc:BuildingNumber': deliveryAddress.apartmentNumber,
                    'cbc:CitySubdivisionName': deliveryAddress.district,
                    'cbc:CityName': deliveryAddress.city,
                    'cbc:PostalZone': deliveryAddress.postalCode,
                    'cbc:Region': '',
                    // 'cbc:District': deliveryAddress.subDistrict,
                    'cac:Country': {
                        'cbc:Name': countryNames[deliveryAddress.countryId]
                    }
                },
                ...(!!carrier
                    ? {
                          'cac:CarrierParty': {
                              'cbc:WebsiteURI': carrier.website,
                              ...(!!carrier.tin
                                  ? {
                                        'cac:PartyIdentification': {
                                            'cbc:ID': {
                                                $: {
                                                    schemeID: 'VKN'
                                                },
                                                _: carrier.tin
                                            }
                                        }
                                    }
                                  : {}),
                              'cac:PartyName': {
                                  'cbc:Name': !_.isEmpty(carrier.legalName) ? carrier.legalName : carrier.name
                              },
                              'cac:PostalAddress': {
                                  'cbc:Room': carrier.address.doorNumber,
                                  'cbc:StreetName': `${carrier.address.subDistrict} ${carrier.address.street}`,
                                  'cbc:BuildingName': '',
                                  'cbc:BuildingNumber': carrier.address.apartmentNumber,
                                  'cbc:CitySubdivisionName': carrier.address.district,
                                  'cbc:CityName': carrier.address.city,
                                  'cbc:PostalZone': carrier.address.postalCode,
                                  'cbc:Region': '',
                                  // 'cbc:District': customer.address.subDistrict,
                                  'cac:Country': {
                                      'cbc:Name': countryNames[carrier.address.countryId]
                                  }
                              }
                          }
                      }
                    : {}),
                'cac:Despatch': {
                    'cbc:ActualDespatchDate': app.datetime
                        .fromJSDate(_.isDate(eWaybill.deliveryDate) ? eWaybill.deliveryDate : issueDate)
                        .startOf('day')
                        .toFormat('yyyy-LL-dd'),
                    'cbc:ActualDespatchTime': app.datetime
                        .fromJSDate(_.isDate(eWaybill.deliveryDate) ? eWaybill.deliveryDate : issueDate)
                        .toFormat('HH:mm:ss')
                }
            },

            ...(!!eWaybill.trailers && eWaybill.trailers.length > 0
                ? {
                      'cac:TransportHandlingUnit': {
                          'cac:TransportEquipment': eWaybill.trailers.map(trailer => ({
                              'cbc:ID': {
                                  $: {
                                      schemeID: 'DORSEPLAKA'
                                  },
                                  _: trailer.licensePlate
                              }
                          }))
                      }
                  }
                : {})
        }
    };

    // Exchange rate.
    if (companyCurrencyName !== currencyName) {
        waybillObj.DespatchAdvice = {
            ...waybillObj.DespatchAdvice,
            'cac:PricingExchangeRate': {
                'cbc:SourceCurrencyCode': currencyName,
                'cbc:TargetCurrencyCode': companyCurrencyName,
                'cbc:CalculationRate': round(eWaybill.currencyRate || 1, 4)
            }
        };
    }

    // Items.
    waybillObj.DespatchAdvice = {
        ...waybillObj.DespatchAdvice,
        'cac:DespatchLine': eWaybill.items.map((item, index) => {
            const product = productsMap[item.productId];
            let note = [];
            if (typeof product === 'object' && product !== null) {
                for (const unitId of Object.keys(product.unitRatios ?? {}).concat(product.baseUnitId)) {
                    const unit = unitsMap[unitId];
                    let ratio = (product.unitRatios ?? {})[unitId] ?? 1;

                    let quantity = item.deliveredQty;
                    if (item.unitId !== product.baseUnitId) {
                        const subRatio = (product.unitRatios ?? {})[item.unitId] ?? 1;

                        quantity *= subRatio;
                    }

                    if (item.unitId === unitId) {
                        continue;
                    }

                    if (_.isFinite(ratio) && ratio > 0 && typeof unit === 'object' && unit !== null) {
                        note.push(`${app.format(quantity / ratio, 'unit')} ${unit.name}`);
                    }
                }
            }

            return {
                'cbc:ID': index + 1,
                ...(note.length > 0
                    ? {
                          'cbc:Note': note.join('<br/>')
                      }
                    : {}),
                'cbc:DeliveredQuantity': {
                    $: {
                        unitCode: unitCodes[item.unitId].trim()
                    },
                    _: item.deliveredQty
                },
                ...(item.requestedQty > item.deliveredQty
                    ? {
                          'cbc:OutstandingQuantity': {
                              $: {
                                  unitCode: unitCodes[item.unitId].trim()
                              },
                              _: item.requestedQty - item.deliveredQty
                          },
                          'cbc:OutstandingReason': item.note || ''
                      }
                    : {}),
                'cac:OrderLineReference': {
                    'cbc:LineID': index + 1
                },
                'cac:Item': {
                    'cbc:Name': !!app.setting('eops.eWaybillUseProductDefinition')
                        ? item.productDefinition
                        : item.description || item.productDefinition,
                    'cac:SellersItemIdentification': {
                        'cbc:ID': !!app.setting('eops.useProductBarcodesAsProductCodesOnEWaybills')
                            ? item.barcode || item.productCode
                            : item.productCode
                    }
                },
                'cac:Shipment': {
                    'cbc:ID': '',
                    'cac:GoodsItem': {
                        'cac:InvoiceLine': {
                            'cbc:ID': '',
                            'cbc:InvoicedQuantity': 0,
                            'cbc:LineExtensionAmount': {
                                $: {
                                    currencyID: currencyName
                                },
                                _: !dontSendUnitPrices ? round(item.total, currencyPrecision) : 0
                            },
                            'cac:Item': {
                                'cbc:Name': item.productDefinition
                            },
                            'cac:Price': {
                                'cbc:PriceAmount': {
                                    $: {
                                        currencyID: currencyName
                                    },
                                    _: !dontSendUnitPrices ? round(item.unitPrice, currencyPrecision) : 0
                                }
                            }
                        }
                    }
                }
            };
        })
    };

    return {
        xml: xmlBuilder.buildObject(waybillObj),
        waybillUUID
    };
}
