import _ from 'lodash';
import schema from './schema';
import fastCopy from 'fast-copy';
import TotalsCurrencyConversions from './_totals-currency-conversions';

export default {
    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            if (this.isReturn) {
                return this.$t('New Vendor Return Invoice');
            }

            if (model.invoiceType === 'exchange-difference') {
                return this.$t('New Exchange Difference Invoice');
            } else if (model.invoiceType === 'due-difference') {
                return this.$t('New Due Difference Invoice');
            } else if (model.invoiceType === 'price-difference') {
                return this.$t('New Price Difference Invoice');
            } else if (model.invoiceType === 'discount') {
                return this.$t('New Discount Invoice');
            }

            return this.$t('New Vendor Invoice');
        },
        schema() {
            return schema(this);
        },
        statuses() {
            const model = this.model;
            const statuses = [{value: 'draft', label: 'Draft'}];

            if (_.isObject(model.paymentPlan) && !_.isEmpty(model.paymentPlan)) {
                statuses.push({
                    value: 'payment-planned',
                    label: 'Payment Planned'
                });
            }

            if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else {
                statuses.push({value: 'approved', label: 'Approved'});
            }

            return statuses;
        },
        status() {
            const model = this.model;

            return model.status;
        },
        assignation() {
            return {
                organizationScope: 'purchase',
                managerField: 'purchaseManagerId',
                employeeField: 'purchaseRepresentativeId',
                disabled:
                    !!this.model.workflowApprovalStatus && this.model.workflowApprovalStatus === 'waiting-for-approval'
            };
        },
        actions() {
            return this.$params('id') &&
                (this.isApproved ||
                    this.isInvoiced ||
                    this.isCanceled ||
                    (!!this.model.workflowApprovalStatus &&
                        this.model.workflowApprovalStatus === 'waiting-for-approval'))
                ? 'edit:disabled,cancel'
                : 'edit,cancel';
        },
        extraActions() {
            const round = this.$app.roundNumber;
            const currencyPrecision = this.$setting('system.currencyPrecision');
            const self = this;

            return [
                {
                    name: 'create-payment',
                    title: 'Create Payment',
                    icon: 'fal fa-coins',
                    handler: this.handleCreatePayment,
                    hidden() {
                        return (
                            !self.$params('id') ||
                            !self.model.paymentTermId ||
                            self.status === 'canceled' ||
                            !!self.model.receiptId ||
                            round(self.model.grandTotal - self.model.plannedTotal || 0, currencyPrecision) === 0 ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'create-delivery',
                    title: 'Create Delivery',
                    icon: 'fal fa-truck-container',
                    handler: this.handleCreateDelivery,
                    hidden() {
                        return (
                            !self.$params('id') ||
                            self.status !== 'approved' ||
                            self.model.invoiceType !== 'purchase' ||
                            self.model.productsShippedWithInvoice ||
                            !(self.model.items || []).some(item => item.productType !== 'service') ||
                            (Array.isArray(self.model.transferIds) && self.model.transferIds.length > 0) ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'update-e-invoice',
                    title: 'Update E-Invoice',
                    icon: 'fal fa-pencil',
                    handler: this.handleUpdateEInvoice,
                    hidden() {
                        return (
                            !self.$params('id') ||
                            !self.isReturn ||
                            self.status !== 'approved' ||
                            self.eInvoiceStatus !== 'draft' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                // {
                //     name: 'create-e-invoice',
                //     title: 'Create Draft E-Invoice',
                //     icon: 'fal fa-briefcase',
                //     handler: this.handleCreateEInvoice,
                //     hidden() {
                //         return !self.$params('id') ||
                //             !self.isReturn ||
                //             self.status !== 'approved' ||
                //             self.model.invoicing !== 'none';
                //     }
                // },
                {
                    name: 'clear-payment-plan',
                    title: 'Clear Payment Plan',
                    icon: 'fal fa-broom',
                    handler: this.handleClearPaymentPlan,
                    hidden() {
                        return (
                            self.$params('isPreview') ||
                            self.status === 'draft' ||
                            self.status === 'approved' ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'copy-from-source',
                    title: 'Copy From Source',
                    icon: 'fal fa-copy',
                    handler: this.handleCopyFromSource,
                    disabled() {
                        return (
                            self.$params('isPreview') ||
                            !self.model.partnerId ||
                            self.status === 'approved' ||
                            self.status === 'canceled' ||
                            (self.model.copiedDocumentRecords || []).length > 0 ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'cancel-invoice',
                    title: 'Cancel Invoice',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    hidden() {
                        return (
                            !self.$params('id') ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                }
                // {
                //     name: 'print-e-invoice',
                //     title: 'Print',
                //     icon: 'fal fa-print',
                //     handler: this.handlePreviewEInvoice,
                //     hidden() {
                //         return !self.$params('id') || self.model.invoiceScenario === 'normal';
                //     }
                // },
                // {
                //     name: 'send-by-email',
                //     title: 'Send By Email',
                //     icon: 'fal fa-envelope',
                //     // handler: this.handleCancel,
                //     get disabled() {
                //         return !self.$params('id')
                //     }
                // },
            ];
        },
        isReturn() {
            return !!this.$params('isReturn');
        },
        isApproved() {
            const model = this.model;

            return model.status === 'approved';
        },
        isCanceled() {
            const model = this.model;

            return model.status === 'canceled';
        },
        isSendEInvoiceDisabled() {
            return (
                this.eInvoiceStatus !== 'draft' ||
                (!!this.model.workflowApprovalStatus && this.model.workflowApprovalStatus === 'waiting-for-approval')
            );
        },
        isSendEInvoiceShown() {
            return !(
                !this.$params('id') ||
                !this.isReturn ||
                this.status !== 'approved' ||
                this.model.invoiceScenario === 'normal'
            );
        },
        invoiceScenarioOptions() {
            if (this.isReturn) {
                return [
                    {value: 'normal', label: 'Normal'},
                    {value: 'basic-invoice', label: 'Basic invoice'},
                    {value: 'e-archive-invoice', label: 'E-archive invoice'}
                ];
            }

            return [
                {value: 'normal', label: 'Normal'},
                {value: 'basic-invoice', label: 'Basic invoice'},
                {value: 'commercial-invoice', label: 'Commercial invoice'},
                {value: 'e-archive-invoice', label: 'E-archive invoice'},
                {value: 'export-invoice', label: 'Export invoice'},
                {value: 'passenger-invoice', label: 'Passenger invoice'},
                {value: 'government-invoice', label: 'Government invoice'}
            ];
        },
        isDocumentNoDisabled() {
            const model = this.model;
            let result = false;

            if (this.isReturn && model.invoiceScenario === 'normal') {
                result = true;
            }

            return result;
        },
        totalItems() {
            const self = this;
            const model = this.model;
            const items = [];

            // Sub total
            items.push({label: this.$t('Subtotal'), value: model.subTotal});

            // Discount
            if (this.$setting('purchase.discounts')) {
                let discountLabel =
                    model.discount > 0
                        ? `${this.$t('Discount')} (%${this.$format(model.discount, 'percentage')})`
                        : this.$t('Discount');

                const item = {
                    label: discountLabel,
                    value: model.discountAmount > 0 ? -model.discountAmount : 0,
                    action: {
                        title: this.$t('Apply Discount'),
                        icon: 'fas fa-badge-percent text-danger',
                        disabled:
                            model.subTotal === 0 ||
                            (this.$params('id') && this.$params('isPreview')) ||
                            this.model.status === 'payment-planned',
                        handler: this.applyDiscount
                    }
                };

                if (model.discountAmount > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Subtotal After Discount'),
                            value: model.subTotalAfterDiscount
                        }
                    ];
                }

                let totalRowDiscountAmount = 0;
                for (const row of model.items || []) {
                    const unDiscountedTotal = this.$app.round(row.quantity * row.unitPrice, 'total');
                    const rowDiscountAmount = this.$app.round(unDiscountedTotal - row.total, 'total');

                    if (rowDiscountAmount > 0) {
                        totalRowDiscountAmount += rowDiscountAmount;
                    }
                }
                if (totalRowDiscountAmount > 0) {
                    if (!Array.isArray(item.subItems)) item.subItems = [];

                    item.subItems.push({
                        label: this.$t('Total Row Discount Amount'),
                        value: totalRowDiscountAmount
                    });
                }

                items.push(item);
            }

            // Tax total.
            if (Array.isArray(model.appliedTaxes) && model.appliedTaxes.length > 0) {
                const groupedTaxes = _.groupBy(model.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));
                const item = {
                    label: this.$t('Taxes'),
                    value: model.taxTotal,
                    subItems: []
                };

                for (const label of Object.keys(groupedTaxes)) {
                    const appliedTaxes = groupedTaxes[label];
                    let total = 0;

                    for (const tax of appliedTaxes) {
                        total += tax.appliedAmount;
                    }

                    item.subItems.push({
                        label,
                        value: this.$app.round(total, 'total')
                    });
                }

                items.push(item);
            } else {
                items.push({label: this.$t('Taxes'), value: 0});
            }

            // Rounding
            items.push({
                label: this.$t('Rounding'),
                value: model.rounding,
                action: {
                    title: this.$t('Apply Rounding'),
                    icon: 'far fa-badge',
                    disabled: model.subTotal === 0 || (this.$params('id') && this.$params('isPreview')),
                    handler: this.applyRounding
                }
            });

            // Grand total
            const grandTotal = {
                label: this.$t('Grand Total'),
                value: model.grandTotal,
                style: {fontWeight: 700},
                action: {
                    title: this.$t('Currency Conversions'),
                    icon: 'fas fa-sync-alt',
                    iconStyle: 'font-size: 12px;',
                    popupComponent: TotalsCurrencyConversions,
                    popupWidth: 320,
                    popupPayload: {
                        systemCurrencyId: this.systemCurrencyId,
                        currencies: this.currencies ?? [],
                        currencyRate: this.model.currencyRate ?? 1,
                        exchangeRates: this.model.exchangeRates ?? []
                    }
                }
            };
            if (model.paidTotal > 0) {
                grandTotal.subItems = [];

                grandTotal.subItems.push({
                    label: this.$t('Total Paid'),
                    value: model.paidTotal || 0
                });
                grandTotal.subItems.push({
                    label: this.$t('Total Remaining'),
                    value: model.grandTotal - (model.paidTotal || 0)
                });
            }
            items.push(grandTotal);

            if (!!this.$setting('system.freight')) {
                const freight = model.freight;
                let amount = 0;

                if (!!freight && freight.amount > 0) {
                    amount = freight.amount;
                }

                const item = {
                    label: this.$t('Freight'),
                    value: amount,
                    action: {
                        title: this.$t('Apply Freight'),
                        icon: 'fas fa-truck',
                        iconStyle: {fontSize: '12px'},
                        handler: this.applyFreight
                    }
                };

                if (!!freight && Array.isArray(freight.appliedTaxes) && freight.appliedTaxes.length > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Freight'),
                            value: amount
                        }
                    ];

                    const groupedTaxes = _.groupBy(freight.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));

                    for (const label of Object.keys(groupedTaxes)) {
                        const appliedTaxes = groupedTaxes[label];
                        let total = 0;

                        for (const tax of appliedTaxes) {
                            total += tax.appliedAmount;
                        }

                        item.subItems.push({
                            label,
                            value: this.$app.round(total, 'total')
                        });
                    }
                }

                items.push(item);
            }

            return {items, format: this.currencyFormat};
        },
        documentTypeIdFilters() {
            const filters = {type: 'invoice'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.documentTypeIds || []
                };
            }

            return filters;
        },
        partnerGroupIdFilters() {
            const filters = {type: 'vendor'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.vendorGroupIds || []
                };
            }

            return filters;
        },
        partnerIdFilters() {
            const filters = {type: 'vendor'};

            if (this.model.partnerGroupId) {
                filters.groupId = this.model.partnerGroupId;
            } else if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupId = {
                    $in: this.organizationSettings.vendorGroupIds || []
                };
            }

            return filters;
        },
        isPartnerIdDisabled() {
            // if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
            //     return !this.model.partnerGroupId || this.status === 'payment-planned';
            // }

            return this.status === 'payment-planned';
        },
        eInvoiceTypeIdOptions() {
            if (this.isReturn) {
                return this.eInvoiceTypes
                    .filter(type => type.scenarios.indexOf(this.model.invoiceScenario) !== -1)
                    .map(type => ({
                        value: type._id,
                        label: type.name
                    }));
            }

            return this.eInvoiceTypes
                .filter(type => type.scenarios.indexOf(this.model.invoiceScenario) !== -1)
                .map(type => ({
                    value: type._id,
                    label: type.name
                }));
        },
        paymentTermIdFilters() {
            const filters = {};

            if (this.isReturn) {
                filters.$or = [{scope: null}, {scope: {$exists: false}}, {scope: 'receipt'}];
            } else {
                const contractParams = this.model.contractParams;

                if (_.isPlainObject(contractParams) && Array.isArray(contractParams.paymentTermIds)) {
                    filters._id = {$in: contractParams.paymentTermIds};
                } else if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                    filters._id = {
                        $in: this.organizationSettings.paymentTermIds || []
                    };
                }

                filters.$or = [{scope: null}, {scope: {$exists: false}}, {scope: 'payment'}];
            }

            return filters;
        },
        listPriceIdFilters() {
            const filters = {status: 'published'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.listPriceIds || []
                };
            }

            return filters;
        },
        guaranteeIdFilters() {
            const contractParams = this.model.contractParams;
            const filters = {
                type: {$in: ['partner-received', 'partner-issued']},
                partnerId: this.model.partnerId,
                status: 'approved'
            };

            if (_.isPlainObject(contractParams) && Array.isArray(contractParams.guaranteeIds)) {
                filters._id = {$in: contractParams.guaranteeIds};
            }

            return filters;
        },
        journalIdFilters() {
            if (this.model.isReturn) {
                return {
                    type: 'purchase-return',
                    branchId: this.model.branchId,
                    $or: [
                        {currencyId: this.model.currencyId},
                        {currencyId: {$exists: false}},
                        {currencyId: ''},
                        {currencyId: null}
                    ]
                };
            }

            return {
                type: 'purchase',
                branchId: this.model.branchId,
                $or: [
                    {currencyId: this.model.currencyId},
                    {currencyId: {$exists: false}},
                    {currencyId: ''},
                    {currencyId: null}
                ]
            };
        },
        accountingAccountIdAttributes() {
            const filters = {
                'tree.hasChild': {$ne: true},
                'tree.path': {
                    $regex: `^${this.mainAccountingAccountPath}`,
                    $options: 'i'
                },
                $sort: {
                    code: 1
                }
            };

            if (this.model.currencyId) {
                filters.$or = [
                    {currencyId: this.model.currencyId},
                    {currencyId: {$exists: false}},
                    {currencyId: ''},
                    {currencyId: null}
                ];
            }

            return {
                collection: 'kernel.accounts',
                filters,
                extraFields: ['code', 'name'],
                template: '{{code}} - {{name}}'
            };
        },
        isDueDateDisabled() {
            const model = this.model;
            return _.isObject(model.paymentPlan) && !_.isEmpty(model.paymentPlan);
        },
        activityPayload() {
            return {
                partnerType: 'vendor',
                partnerId: this.model.partnerId
            };
        },
        isScopeBalanceAchieved() {
            const model = this.model;
            const scopeRate = model.scopeRate || 1;
            const paymentScope = (model.paymentPlan || {}).scopeRate;

            return scopeRate === paymentScope;
        },
        itemsTableOptions() {
            const self = this;

            return {
                rowClassRules: {
                    'bg-purple'(params) {
                        return _.isPlainObject(params.data) && !!params.data.contractApplied;
                    },
                    'bg-orange'(params) {
                        return (
                            _.isPlainObject(params.data) &&
                            !params.data.contractApplied &&
                            !!params.data.discountListApplied
                        );
                    }
                }
            };
        },
        itemsContextMenuActions() {
            return [
                {
                    title: this.$t('Stock Status'),
                    icon: 'inventory',
                    disabled: row => {
                        return !(!!row.product && !!row.warehouseId);
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.stock-status.master',
                            params: {
                                productId: row.productId,
                                warehouseId: row.warehouseId,
                                isADPGraphShown: true
                            }
                        });
                    }
                },
                {
                    title: this.$t('Expiration Analysis'),
                    icon: 'calendar-exclamation',
                    disabled: row => {
                        return !(!!row.product && row.product.tracking === 'serial');
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.expiration-analysis',
                            params: {
                                productId: row.productId
                            }
                        });
                    }
                },
                {
                    title: this.$t('Unit Price Analysis'),
                    icon: 'coins',
                    disabled: row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('purchase.allowedUnitPriceAnalysisTypes');

                        return !(
                            !!row.product &&
                            Array.isArray(allowedUnitPriceAnalysisTypes) &&
                            allowedUnitPriceAnalysisTypes.length > 0
                        );
                    },
                    action: async row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('purchase.allowedUnitPriceAnalysisTypes');

                        this.$program.dialog({
                            component: 'purchase.components.unit-price-analysis',
                            params: {
                                documentCollection: 'accounting.vendor-invoices',
                                documentId: this.$params('id'),
                                documentCode: this.model.code,
                                partnerId: this.model.partnerId,
                                leadId: null,
                                productId: row.productId,
                                productCode: row.product.code,
                                productDefinition: row.product.definition,
                                unitPrice: row.unitPrice,
                                currencyId: this.model.currencyId,
                                currencyRate: this.model.currencyRate,
                                currencyFormat: this.currencyFormat,
                                allowedUnitPriceAnalysisTypes,
                                forcedPreview: this.$params('isPreview'),
                                handleApply: async unitPrice => {
                                    this.$params('loading', true);

                                    const itemIndex = this.model.items.findIndex(item => item.id === row.id);
                                    const updatedItems = await this.$rpc('accounting.decorate-vendor-invoice-items', {
                                        items: [
                                            {
                                                ...this.model.items[itemIndex],
                                                unitId: row.baseUnitId,
                                                unitPrice
                                            }
                                        ],
                                        field: 'unitId',
                                        model: _.omit(this.model, 'items'),
                                        productFields: this.productFields,
                                        dontSetUnitPrice: true
                                    });

                                    const items = [...this.model.items];
                                    items[itemIndex] = updatedItems[0];

                                    this.$set(this.model, 'items', items);
                                    this.afterSaveItem();

                                    this.$params('loading', false);
                                }
                            }
                        });
                    }
                },
                ...(this.$app.hasModule('pcm')
                    ? [
                          {
                              title: this.$t('Configure Product'),
                              icon: 'cog',
                              disabled: row => {
                                  return !(!!row._id && !!row.productId && !!row.pcmModelId);
                              },
                              action: async row => {
                                  this.$params('loading', true);

                                  try {
                                      const onSubmit = async ({
                                          configuratorType,
                                          description,
                                          configurationId,
                                          deliveryDate,
                                          quantity,
                                          price,
                                          additionalPrice,
                                          products,
                                          hash
                                      }) => {
                                          this.$params('loading', true);

                                          try {
                                              const items = fastCopy(this.model.items);
                                              const itemsIndex = items.findIndex(i => i.id === row.id);

                                              if (configuratorType !== 'product-finder') {
                                                  const item = (
                                                      await this.$rpc('accounting.decorate-vendor-invoice-items', {
                                                          items: [
                                                              {
                                                                  ...row,
                                                                  description,
                                                                  pcmConfigurationId: configurationId,
                                                                  pcmHash: hash,
                                                                  scheduledDate: deliveryDate,
                                                                  quantity,
                                                                  baseQuantity:
                                                                      quantity * (row.baseQuantity / row.quantity)
                                                                  // total: price
                                                              }
                                                          ],
                                                          field: 'quantity',
                                                          model: _.omit(this.model, 'items'),
                                                          productFields: this.productFields
                                                      })
                                                  )[0];

                                                  items[itemsIndex] = item;
                                              } else {
                                                  items.splice(itemsIndex, 1);
                                              }

                                              if (products.length > 0) {
                                                  const additionalItems = await this.$rpc(
                                                      'accounting.decorate-vendor-invoice-items',
                                                      {
                                                          items: products.map(row => {
                                                              return {
                                                                  productId: row.productId,
                                                                  scheduledDate: deliveryDate,
                                                                  quantity: row.quantity,
                                                                  baseQuantity: row.quantity,
                                                                  unitPrice: row.unitPrice,
                                                                  discount: 0,
                                                                  unitPriceAfterDiscount: 0,
                                                                  grossUnitPriceAfterDiscount: 0,
                                                                  taxTotal: 0,
                                                                  grossTotal: 0,
                                                                  stockQuantity: 0,
                                                                  orderedQuantity: 0,
                                                                  assignedQuantity: 0,
                                                                  availableQuantity: 0,
                                                                  warehouseStockQuantity: 0,
                                                                  warehouseOrderedQuantity: 0,
                                                                  warehouseAssignedQuantity: 0,
                                                                  warehouseAvailableQuantity: 0,
                                                                  total: 0
                                                              };
                                                          }),
                                                          field: 'productId',
                                                          model: _.omit(this.model, 'items'),
                                                          productFields: this.productFields,
                                                          dontSetUnitPrice: true
                                                      }
                                                  );

                                                  items.push(...additionalItems);
                                              }

                                              this.model.items = items;

                                              // Reset totals.
                                              this.model.discount = await this.calculateGeneralDiscount();
                                              this.model.paymentPlan = null;
                                              this.model.paymentPlanningDate = null;
                                              await this.calculateTotals();

                                              this.$nextTick(() => {
                                                  this.itemsKey = _.uniqueId('invoiceItems_');

                                                  this.$program.message(
                                                      'success',
                                                      this.$t('Product configuration applied successfully.')
                                                  );
                                              });
                                          } catch (error) {
                                              this.$program.message('error', error.message);
                                          }

                                          this.$params('loading', false);
                                      };
                                      const params = {
                                          title: row.productDefinition,
                                          forcedPreview: this.$params('isPreview'),
                                          modelId: row.pcmModelId,
                                          price: row.unitPrice,
                                          quantity: row.quantity,
                                          deliveryDate: row.scheduledDate,
                                          configurationId: row.pcmConfigurationId,
                                          currencyFormat: this.currencyFormat,
                                          currencyId: this.model.currencyId,
                                          currencyRate: this.model.currencyRate || 1,
                                          listPriceId: this.model.listPriceId,
                                          issueDate: this.model.issueDate,
                                          referenceCollection: 'accounting.vendor-invoices',
                                          referenceView: 'accounting.purchase.vendor-invoices',
                                          referenceId: this.$params('id'),
                                          referenceCode: this.model.code,
                                          partnerId: this.model.partnerId,
                                          onSubmit
                                      };

                                      if (!!row.pcmConfigurationId) {
                                          const configuration = await this.$collection('pcm.configurations').findOne({
                                              _id: row.pcmConfigurationId,
                                              $disableSoftDelete: true,
                                              $disableActiveCheck: true
                                          });

                                          params.price = configuration.payload.price;
                                          params.quantity = configuration.payload.quantity;
                                          params.deliveryDate = configuration.payload.deliveryDate;
                                          params.values = configuration.payload.values;
                                          params.stepId = configuration.payload.stepId;
                                          params.finder = configuration.payload.finder;
                                      }

                                      this.$program.dialog({
                                          component: 'pcm.components.configuration',
                                          params
                                      });
                                  } catch (error) {
                                      this.$program.message('error', error.message);
                                  }
                              }
                          }
                      ]
                    : [])
            ];
        },
        financialProjectIdFilters() {
            return {
                $and: [
                    {
                        $or: [
                            {validFrom: {$exists: false}},
                            {validFrom: {$eq: null}},
                            {validFrom: {$lte: this.model.issueDate}}
                        ]
                    },
                    {
                        $or: [
                            {validTo: {$exists: false}},
                            {validTo: {$eq: null}},
                            {validTo: {$gte: this.model.issueDate}}
                        ]
                    }
                ],
                $sort: {code: 1}
            };
        },
        finalRelatedDocuments() {
            const relatedDocuments = this.relatedDocuments || [];
            const modelRelatedDocuments = this.model.relatedDocuments || [];
            const rds = [...relatedDocuments];

            for (const mrd of modelRelatedDocuments) {
                const existingIndex = rds.findIndex(rd => rd.collection === mrd.collection);

                if (existingIndex !== -1) {
                    rds[existingIndex].ids = _.uniq([...(rds[existingIndex].ids || []), ...(mrd.ids || [])]);
                } else {
                    rds.push({
                        ...mrd
                    });
                }
            }

            return rds;
        },
        warehouseIdFilters() {
            const filters = {branchId: this.model.branchId};

            if (
                _.isObject(this.organizationSettings) &&
                !_.isEmpty(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.warehouseIds) &&
                this.organizationSettings.warehouseIds.length > 0
            ) {
                filters._id = {
                    $in: this.organizationSettings.warehouseIds || []
                };
            }

            return filters;
        },
        isReturnEInvoice() {
            const eInvoiceType = this.eInvoiceTypes.find(type => type._id === this.model.eInvoiceTypeId);

            return !!eInvoiceType && eInvoiceType.code === 'IADE';
        }
    }
};
