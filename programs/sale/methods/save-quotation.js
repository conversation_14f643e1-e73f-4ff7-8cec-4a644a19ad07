import _ from 'lodash';

export default {
    name: 'save-quotation',
    async action({data, id}, params) {
        const app = this.app;
        const isCreate = !id;
        const quotationsCollection = app.collection('sale.quotations');

        // Check items.
        if (Array.isArray(data.items)) {
            for (const item of data.items) {
                // Check product.
                if (!item.productId) {
                    throw new app.errors.Unprocessable(this.translate('Each row must have a product defined!'));
                }

                // Check row total.
                if (item.total < 0) {
                    throw new app.errors.Unprocessable(this.translate('Row total cannot be lower than zero!'));
                }
            }
        }

        // Exchange rates map.
        const exchangeRatesMap = {};
        for (const exchangeRate of data.exchangeRates || []) {
            exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
        }
        data.exchangeRatesMap = exchangeRatesMap;

        // Check quotas.
        await app.rpc('sale.quotas-check', {
            type: 'order',
            partnerId: data.partnerId,
            date: data.quotationDate,
            items: data.items.map(item => _.pick(item, ['productId', 'baseQuantity']))
        });

        // Init limit params.
        data.limitParams = await app.rpc('finance.check-partner-limit', {
            partnerId: data.partnerId,
            currencyId: data.currencyId,
            guaranteeId: data.guaranteeId,
            amount: data.grandTotal,
            document: 'quotation'
        });

        if (!isCreate) {
            // Get existing quotation.
            const quotation = await quotationsCollection.findOne({
                _id: id,
                $select: ['status', 'expiryDate', 'items']
            });

            if (
                typeof data.isExpired === 'boolean' &&
                data.isExpired !== false &&
                data.status !== 'canceled' &&
                app.setting('sale.forceQuotationValidity')
            ) {
                const expiryDate = app.datetime.fromJSDate(quotation.expiryDate);
                const now = app.datetime.local();

                if (now.diff(expiryDate).as('days') > 0) {
                    throw new app.errors.Unprocessable(
                        this.translate('The quotation cannot be saved because it has expired!')
                    );
                }
            }

            // Check if we have a quotation with this id.
            if (!_.isObject(quotation)) {
                throw new app.errors.Unprocessable(this.translate('Quotation not found!'));
            }

            // Check if document has at least one item.
            if (data.status === 'won' && quotation.items.length < 1) {
                throw new app.errors.Unprocessable(this.translate('Document rows must have at least one item!'));
            }

            // Check if quotation is already approved or canceled.
            // if (quotation.status === 'won' || quotation.status === 'canceled') {
            //     throw new app.errors.Unprocessable(
            //         this.translate('Approved or canceled quotations cannot be updated!')
            //     );
            // }

            // Initialize extras.
            const extra = {
                basePrice: 0,
                grossProfit: 0,
                profitRate: 0,
                profitMargin: 0,
                cashAmount: 0,
                cashInstallmentCount: 0,
                moneyTransferAmount: 0,
                moneyTransferInstallmentCount: 0,
                chequeAmount: 0,
                chequeInstallmentCount: 0,
                promissoryNoteAmount: 0,
                promissoryNoteInstallmentCount: 0,
                posAmount: 0,
                posInstallmentCount: 0,
                installmentCount: 0
            };
            if (_.isPlainObject(data.paymentPlan) && !_.isEmpty(data.paymentPlan)) {
                const paymentPlan = data.paymentPlan;
                let installmentCount = 0;

                for (const i of paymentPlan.items || []) {
                    if (!_.isNumber(extra[`${i.documentType}Amount`])) extra[`${i.documentType}Amount`] = 0;
                    if (!_.isNumber(extra[`${i.documentType}InstallmentCount`]))
                        extra[`${i.documentType}InstallmentCount`] = 0;
                    if (!_.isNumber(extra[`${i.documentType}Amount`])) extra[`${i.documentType}Amount`] = 0;

                    extra[`${i.documentType}Amount`] += i.total;

                    if (i.documentType === 'pos') {
                        extra[`${i.documentType}InstallmentCount`] += i.installmentCount;

                        installmentCount += i.installmentCount;
                    } else {
                        extra[`${i.documentType}InstallmentCount`]++;

                        installmentCount++;
                    }
                }

                for (const documentType of Object.keys(_.groupBy(paymentPlan.items || [], 'documentType'))) {
                    if (_.isNumber(extra[`${documentType}Amount`])) {
                        extra[`${documentType}Amount`] = app.round(extra[`${documentType}Amount`], 'total');
                    }
                }

                if (installmentCount > 0) {
                    extra.installmentCount = installmentCount;
                }
            }
            const profit = await app.collection('sale.document-profits').findOne({
                documentId: id,
                documentCollection: 'sale.quotations',
                $select: ['totalBasePrice', 'totalGrossProfit', 'profitRate', 'profitMargin']
            });
            if (_.isPlainObject(profit)) {
                extra.basePrice = profit.totalBasePrice;
                extra.grossProfit = profit.totalGrossProfit;
                extra.profitRate = profit.profitRate;
                extra.profitMargin = profit.profitMargin;
            }
            for (const key of Object.keys(extra)) {
                data[`extra.${key}`] = extra[key];
            }

            if (data.status === 'canceled') {
                data.convertedAt = app.datetime.local().toJSDate();

                // Remove partner limit transaction if exists.
                await app.collection('finance.partner-limit-transactions').remove({
                    documentId: id,
                    documentCollection: 'sale.quotations'
                });
            }

            // Run assignation, and validation workflow.
            if (app.hasModule('workflow')) {
                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'sale.quotations',
                        data: data,
                        id,
                        operation: 'update',
                        actionTypes: ['assignation', 'validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            const result = await quotationsCollection.patch({_id: id}, data, {
                user: params.user,
                userLocation: params.userLocation,
                setLocation: true,
                checkPermission: true
            });

            // Create partner limit.
            if (data.status === 'approved') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: quotation.partnerId,
                    $select: ['currencyId', 'enableLimitChecks', 'limitControlDocument'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (partner.enableLimitChecks && partner.limitControlDocument === 'quotation') {
                    const limit = {};

                    limit.partnerType = 'customer';
                    limit.partnerId = partner._id;
                    limit.guaranteeId = data.guaranteeId;
                    limit.date = app.datetime.local().toJSDate();
                    limit.documentCollection = 'sale.quotations';
                    limit.documentView = 'sale.sales.quotations-detail';
                    limit.documentId = id;
                    limit.currencyId = partner.currencyId;
                    limit.amount = -data.grandTotal;

                    await app.collection('finance.partner-limit-transactions').create(limit, {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    });
                }
            }

            // Run notification workflow.
            if (app.hasModule('workflow')) {
                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'sale.quotations',
                        data: result,
                        id,
                        operation: 'update',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            return result;
        } else {
            const numbering = await app.collection('kernel.numbering').findOne({
                code: 'salesQuotationNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            data.code = await app.rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save: false
            });
            data.stages = await app.collection('sale.quotation-stages').find();

            // Run validation workflow.
            if (app.hasModule('workflow')) {
                const flowResult = await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'sale.quotations',
                        data: {
                            ...data,
                            extra: {
                                basePrice: 0,
                                grossProfit: 0,
                                profitRate: 0,
                                profitMargin: 0,
                                cashAmount: 0,
                                cashInstallmentCount: 0,
                                moneyTransferAmount: 0,
                                moneyTransferInstallmentCount: 0,
                                chequeAmount: 0,
                                chequeInstallmentCount: 0,
                                promissoryNoteAmount: 0,
                                promissoryNoteInstallmentCount: 0,
                                posAmount: 0,
                                posInstallmentCount: 0,
                                installmentCount: 0
                            }
                        },
                        operation: 'create',
                        actionTypes: ['validation']
                    },
                    {user: params.user}
                );
                data = flowResult.data;
            }

            data.code = await app.rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save: true
            });

            const result = await quotationsCollection.create(
                {
                    ...data,
                    extra: {
                        basePrice: 0,
                        grossProfit: 0,
                        profitRate: 0,
                        profitMargin: 0,
                        cashAmount: 0,
                        cashInstallmentCount: 0,
                        moneyTransferAmount: 0,
                        moneyTransferInstallmentCount: 0,
                        chequeAmount: 0,
                        chequeInstallmentCount: 0,
                        promissoryNoteAmount: 0,
                        promissoryNoteInstallmentCount: 0,
                        posAmount: 0,
                        posInstallmentCount: 0,
                        installmentCount: 0
                    }
                },
                {
                    user: params.user,
                    userLocation: params.userLocation,
                    setLocation: true,
                    checkPermission: true
                }
            );

            // Create partner limit.
            if (data.status === 'approved') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: data.partnerId,
                    $select: ['currencyId', 'enableLimitChecks', 'limitControlDocument'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (partner.enableLimitChecks && partner.limitControlDocument === 'quotation') {
                    const limit = {};

                    limit.partnerType = 'customer';
                    limit.partnerId = partner._id;
                    limit.guaranteeId = data.guaranteeId;
                    limit.date = app.datetime.local().toJSDate();
                    limit.documentCollection = 'sale.quotations';
                    limit.documentView = 'sale.sales.quotations-detail';
                    limit.documentId = result._id;
                    limit.currencyId = partner.currencyId;
                    limit.amount = -data.grandTotal;

                    await app.collection('finance.partner-limit-transactions').create(limit, {
                        user: params.user,
                        userLocation: params.userLocation,
                        setLocation: true
                    });
                }
            }

            // Run notification workflow.
            if (app.hasModule('workflow')) {
                await app.rpc(
                    'workflow.run-workflow',
                    {
                        name: 'sale.quotations',
                        data: result,
                        operation: 'create',
                        actionTypes: ['notification']
                    },
                    {user: params.user}
                );
            }

            // PCM.
            if (app.hasModule('pcm')) {
                const operations = [];

                for (const item of result.items.filter(item => !!item.pcmConfigurationId)) {
                    operations.push({
                        updateOne: {
                            filter: {_id: item.pcmConfigurationId},
                            update: {$set: {referenceId: result._id}}
                        }
                    });
                }

                if (operations.length > 0) {
                    await app.collection('pcm.configurations').bulkWrite(operations);
                }
            }

            // Conversion record.
            const conversion = {};
            conversion.quotationId = result._id;
            conversion.quotationCode = result.code;
            conversion.quotationRecordDate = result.recordDate;
            conversion.branchId = result.branchId;
            conversion.organizationId = result.organizationId;
            conversion.salesManagerId = result.salesManagerId;
            conversion.salespersonId = result.salespersonId;
            conversion.sourceId = result.sourceId;
            conversion.communicationChannelId = result.communicationChannelId;
            if (result.partnerType === 'customer') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: result.partnerId,
                    $select: ['_id', 'code', 'createdAt']
                });
                conversion.customerId = partner._id;
                conversion.customerCode = partner.code;
                conversion.customerRecordDate = partner.createdAt;
            } else {
                const lead = await app.collection('crm.leads').findOne({
                    _id: result.leadId,
                    $select: ['_id', 'code', 'recordDate']
                });

                conversion.leadId = lead._id;
                conversion.leadCode = lead.code;
                conversion.leadRecordDate = lead.recordDate;
            }
            conversion.date = app.datetime.local().toJSDate();
            conversion.status = 'quotation';
            app.collection('sale.conversions').create(conversion);

            return result;
        }
    }
};
