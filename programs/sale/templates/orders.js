import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';

export default {
    name: 'order',
    title: 'Order',
    contentType: 'html',
    outputType: 'pdf',
    async getLocale(app, recordId) {
        if (!!recordId) {
            const document = await app.collection('sale.orders').findOne({
                _id: recordId,
                $select: ['partnerId'],
                $disableSoftDelete: true
            });

            if (!!document) {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: document.partnerId,
                    $select: ['languageId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!partner) {
                    const language = await app.collection('kernel.languages').findOne({
                        _id: partner.languageId,
                        $select: ['isoCode'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!language) {
                        return language.isoCode;
                    }
                }
            }
        }

        const company = await app.collection('kernel.company').findOne({});

        return company.language.isoCode;
    },
    async qr(app, recordId = '', data = {}) {
        return {
            text: `sale.sales.orders.detail/${recordId}`
        };
    },
    async record(app, recordId, params) {
        const round = n => app.roundNumber(n, 2);
        const d = await app.collection('sale.orders').findOne({
            _id: recordId,
            $populate: [
                {
                    field: 'partner',
                    query: {
                        $select: [
                            'code',
                            'name',
                            'isCompany',
                            'legalName',
                            'email',
                            'languageId',
                            'currencyId',
                            'website',
                            'identity',
                            'tin',
                            'taxDepartment',
                            'phone',
                            'phoneNumbers',
                            'address'
                        ]
                    }
                },
                {field: 'organization', query: {$select: ['name']}},
                {
                    field: 'salesManager',
                    query: {$select: ['name', 'positionId', 'positionId', 'occupationId', 'email', 'phone']}
                },
                {
                    field: 'salesperson',
                    query: {$select: ['name', 'positionId', 'positionId', 'occupationId', 'email', 'phone']}
                },
                {
                    field: 'contactPerson',
                    query: {
                        $select: [
                            'code',
                            'name',
                            'isCompany',
                            'legalName',
                            'email',
                            'languageId',
                            'currencyId',
                            'website',
                            'identity',
                            'tin',
                            'taxDepartment',
                            'phone',
                            'phoneNumbers',
                            'address'
                        ]
                    }
                }
            ],
            $disableSoftDelete: true
        });
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        // const company = await app.collection('kernel.company').findOne({});
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'payment-planned', label: 'Payment Planned'},
            {value: 'approved', label: 'Approved'},
            {value: 'to-invoice', label: 'To Invoice'},
            {value: 'invoiced', label: 'Invoiced'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const document = {};

        // Format.
        const currency = await app.collection('kernel.currencies').findOne({
            _id: d.currencyId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const formatOptions = {
            currency: {
                symbol: currency.symbol,
                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            },
            useOutputPrecision: true
        };
        const f = (amount, type) => app.format(amount, type, formatOptions);
        const documentType = await app.collection('sale.document-types').findOne({
            _id: d.documentTypeId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        document.locale = locale;
        document.status = t(statusOptions.find(o => o.value === d.status).label);
        document.code = d.code;
        document.partnerType = d.partnerType;
        document.reference = d.reference;
        document.documentType = (documentType || {}).name;
        if (!!d.partnerGroupId) {
            document.partnerGroup = (
                (await app.collection('kernel.partner-groups').findOne({
                    _id: d.partnerGroupId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }

        document.content = d.content ? d.content.replace(/<[^>]*>/g, '') : '';

        document.contactPerson = '';
        document.currency = currency.name;
        document.currencyRate = f(d.currencyRate, 'decimal');
        document.recordDate = f(d.recordDate, 'datetime');
        document.orderDate = f(d.orderDate, 'date');
        document.scheduledDate = f(d.scheduledDate, 'date');
        document.note = (d.note || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
        document.partnerOrderReference = d.partnerOrderReference;
        document.paymentTerm = '';
        if (!!d.paymentTerm && !!d.paymentTerm.name) {
            document.paymentTerm = d.paymentTerm.name;
        }
        document.exchangeRates = [];
        if (Array.isArray(d.exchangeRates) && d.exchangeRates.length > 0) {
            document.exchangeRates = d.exchangeRates;
        }

        // Totals.
        document.subTotal = f(d.subTotal, 'total');
        document.discount = f(d.discount, 'percentage');
        document.discountAmount = f(d.discountAmount, 'total');
        document.subTotalAfterDiscount = f(d.subTotalAfterDiscount, 'total');
        document.taxTotal = f(d.taxTotal, 'total');
        document.rounding = f(d.rounding, 'total');
        document.grandTotal = f(d.grandTotal, 'total');
        document.appliedTaxes = d.appliedTaxes.map(tax => ({
            name: tax.name,
            label: tax.label,
            percentage: f(tax.amount, 'percentage'),
            amount: f(tax.appliedAmount, 'total')
        }));

        if (!!d.partner) {
            // Partner.
            const partner = {};
            partner.code = d.partner.code;
            partner.isCompany = d.partner.isCompany;
            partner.name = d.partner.name;
            partner.legalName = d.partner.legalName;
            partner.email = d.partner.email;
            partner.language = (
                (await app.collection('kernel.languages').findOne({
                    _id: d.partner.languageId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.currency = (
                (await app.collection('kernel.currencies').findOne({
                    _id: d.partner.currencyId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.website = d.partner.website;
            partner.identity = d.partner.identity;
            partner.tin = d.partner.tin;
            partner.taxDepartment = d.partner.taxDepartment;
            partner.phone = d.partner.phone;
            partner.address = d.partner.address.address;
            partner.country = (
                (await app.collection('kernel.countries').findOne({
                    _id: d.partner.address.countryId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            partner.city = d.partner.address.city;
            partner.district = d.partner.address.district;
            partner.subDistrict = d.partner.address.subDistrict;
            partner.street = d.partner.address.street;
            partner.doorNumber = d.partner.address.doorNumber;
            partner.apartmentNumber = d.partner.address.apartmentNumber;
            partner.postalCode = d.partner.address.postalCode;
            document.partner = partner;
        }

        if (!!d.contactPerson) {
            const contact = {};

            contact.code = d.contactPerson.code;
            contact.isCompany = d.contactPerson.isCompany;
            contact.name = d.contactPerson.name;
            contact.legalName = d.contactPerson.legalName;
            contact.email = d.contactPerson.email;
            contact.language = (
                (await app.collection('kernel.languages').findOne({
                    _id: d.contactPerson.languageId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            contact.currency = (
                (await app.collection('kernel.currencies').findOne({
                    _id: d.contactPerson.currencyId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            contact.website = d.contactPerson.website;
            contact.identity = d.contactPerson.identity;
            contact.tin = d.contactPerson.tin;
            contact.taxDepartment = d.contactPerson.taxDepartment;
            contact.phone = d.contactPerson.phone;

            if (d.contactPerson.address) {
                contact.address = d.contactPerson.address.address;
                contact.country = (
                    (await app.collection('kernel.countries').findOne({
                        _id: d.contactPerson.address.countryId,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    })) || {}
                ).name;
                contact.city = d.contactPerson.address.city;
                contact.district = d.contactPerson.address.district;
                contact.subDistrict = d.contactPerson.address.subDistrict;
                contact.street = d.contactPerson.address.street;
                contact.doorNumber = d.contactPerson.address.doorNumber;
                contact.apartmentNumber = d.contactPerson.address.apartmentNumber;
                contact.postalCode = d.contactPerson.address.postalCode;
            }

            document.contactPerson = d.contactPerson.name;
            document.contact = contact;
        }

        // Delivery address.
        const deliveryAddress = {};
        deliveryAddress.country = (
            (await app.collection('kernel.countries').findOne({
                _id: d.deliveryAddress.countryId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            })) || {}
        ).name;
        deliveryAddress.address = d.deliveryAddress.address;
        deliveryAddress.city = d.deliveryAddress.city;
        deliveryAddress.district = d.deliveryAddress.district;
        deliveryAddress.subDistrict = d.deliveryAddress.subDistrict;
        deliveryAddress.street = d.deliveryAddress.street;
        deliveryAddress.doorNumber = d.deliveryAddress.doorNumber;
        deliveryAddress.apartmentNumber = d.deliveryAddress.apartmentNumber;
        deliveryAddress.postalCode = d.deliveryAddress.postalCode;
        document.deliveryAddress = deliveryAddress;


        // Invoice address.
        const invoiceAddress = {};
        invoiceAddress.country = (
            (await app.collection('kernel.countries').findOne({
                _id: d.invoiceAddress.countryId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            })) || {}
        ).name;
        invoiceAddress.address = d.invoiceAddress.address;
        invoiceAddress.city = d.invoiceAddress.city;
        invoiceAddress.district = d.invoiceAddress.district;
        invoiceAddress.subDistrict = d.invoiceAddress.subDistrict;
        invoiceAddress.street = d.invoiceAddress.street;
        invoiceAddress.doorNumber = d.invoiceAddress.doorNumber;
        invoiceAddress.apartmentNumber = d.invoiceAddress.apartmentNumber;
        invoiceAddress.postalCode = d.invoiceAddress.postalCode;
        invoiceAddress.invoiceAddress = invoiceAddress;

        // Partner info.
        let partnerName = '';
        let partnerAddress = '';
        if (!!document.partner) {
            partnerName = document.partner.name;

            if (document.partner.isCompany) {
                partnerName = document.partner.legalName;
            }

            partnerAddress = invoiceAddress.address;

            if (!!d.invoiceAddressId) {
                const contact = await app.collection('kernel.contacts').findOne({
                    _id: d.invoiceAddressId,
                    $select: ['name', 'address'],
                    $disableSoftDelete: true
                });

                if (!!contact) {
                    partnerName = contact.name;
                    partnerAddress = contact.address.address;
                }
            }
        } else {
            partnerAddress = invoiceAddress.address;
        }
        document.partnerName = partnerName;
        document.partnerAddress = partnerAddress;

        // Additional İnfo.
        document.additionalInformation = [];
        if (_.isPlainObject(d.additionalInformation)) {
            for (const [label, value] of Object.entries(d.additionalInformation)) {
                document.additionalInformation.push({
                    name: label,
                    value: value
                });
            }
        }

        // Organization.
        document.organization = (d.organization || {}).name;
        document.salesManager = (d.salesManager || {}).name;
        document.salesManagerPosition = '';
        document.salesManagerOccupation = '';
        if (!!d.salesManager) {
            if (!!d.salesManager.positionId) {
                const position = await app.collection('kernel.positions').findOne({
                    _id: d.salesManager.positionId,
                    $select: ['name'],
                    $disableSoftDelete: true
                });

                if (!!position) {
                    document.salesManagerPosition = position.name;
                }
            }

            if (!!d.salesManager.occupationId) {
                const occupation = await app.collection('kernel.occupations').findOne({
                    _id: d.salesManager.occupationId,
                    $select: ['name'],
                    $disableSoftDelete: true
                });

                if (!!occupation) {
                    document.salesManagerOccupation = occupation.name;
                }
            }
        }
        document.salesManagerEmail = (d.salesManager || {}).email || '';
        document.salesManagerPhone = (d.salesManager || {}).phone || '';
        document.salesperson = (d.salesperson || {}).name;
        document.salespersonPosition = '';
        document.salespersonOccupation = '';
        if (!!d.salesperson) {
            if (!!d.salesperson.positionId) {
                const position = await app.collection('kernel.positions').findOne({
                    _id: d.salesperson.positionId,
                    $select: ['name'],
                    $disableSoftDelete: true
                });

                if (!!position) {
                    document.salespersonPosition = position.name;
                }
            }

            if (!!d.salesperson.occupationId) {
                const occupation = await app.collection('kernel.occupations').findOne({
                    _id: d.salesperson.occupationId,
                    $select: ['name'],
                    $disableSoftDelete: true
                });

                if (!!occupation) {
                    document.salespersonOccupation = occupation.name;
                }
            }
        }
        document.salespersonEmail = (d.salesperson || {}).email || '';
        document.salespersonPhone = (d.salesperson || {}).phone || '';

        // Logistic.
        const deliveryPriorityOptions = [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ];
        const deliveryPolicyOptions = [
            {value: 'when-one-ready', label: 'When a product is ready'},
            {value: 'when-all-ready', label: 'When all products are ready'}
        ];
        const shippingPaymentTypeOptions = [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ];
        if (!!d.warehouseId) {
            document.warehouse = (
                (await app.collection('inventory.warehouses').findOne({
                    _id: d.warehouseId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        if (!!d.deliveryPriority) {
            document.deliveryPriority = t(deliveryPriorityOptions.find(o => o.value === d.deliveryPriority).label);
        }
        if (!!d.deliveryPolicy) {
            document.deliveryPolicy = t(deliveryPolicyOptions.find(o => o.value === d.deliveryPolicy).label);
        }
        if (!!d.deliveryConditionId) {
            document.deliveryCondition = (
                (await app.collection('logistics.delivery-conditions').findOne({
                    _id: d.deliveryConditionId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        if (!!d.deliveryMethodId) {
            document.deliveryMethod = (
                (await app.collection('logistics.delivery-methods').findOne({
                    _id: d.deliveryMethodId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        if (!!d.carrierId) {
            document.carrier = (
                (await app.collection('logistics.carriers').findOne({
                    _id: d.carrierId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
        }
        document.cargoTrackingCode = d.cargoTrackingCode || '';
        if (!!d.shippingPaymentType) {
            document.shippingPaymentType = t(
                shippingPaymentTypeOptions.find(o => o.value === d.shippingPaymentType).label
            );
        }

        // Items
        const products = await app.collection('inventory.products').find({
            _id: {$in: (d.items || []).map(item => item.productId)},
            $select: [
                'type',
                'isSimple',
                'image',
                'barcode',
                'description',
                'hsCode',
                'unitMeasurements',
                'countryOfOriginId',
                'salesNote'
            ],
            $populate: [{field: 'brand', query: {$select: ['code', 'name']}}],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const productsMap = {};
        for (const product of products) {
            productsMap[product._id] = product;
        }
        const units = await app.collection('kernel.units').find({
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const unitsMap = {};
        for (const unit of units) {
            unitsMap[unit._id] = unit;
        }
        const lengthUnit = await app.collection('kernel.units').findOne({
            category: 'length',
            symbol: 'm',
            $select: ['symbol'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const weightUnit = await app.collection('kernel.units').findOne({
            category: 'weight',
            symbol: 'kg',
            $select: ['symbol'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        let totalQty = 0;
        let totalWeight = 0;
        const items = [];
        for (const i of d.items || []) {
            const product = productsMap[i.productId];
            const unit = unitsMap[i.unitId];
            const baseUnit = unitsMap[i.baseUnitId];
            const item = {};

            const tax = await app.collection('kernel.taxes').findOne({
                _id: i.taxId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            let countryOfOrigin = null;
            if (!!product.countryOfOriginId) {
                countryOfOrigin = await app.collection('kernel.countries').findOne({
                    _id: product.countryOfOriginId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true,
                    $disableTranslation: true
                });
            }

            item.id = i.id;
            item.productImage = !!product.image
                ? app.absoluteUrl(`files/${product.image}`)
                : app.absoluteUrl(`static/images/no-image.png`);
            item.productCode = i.productCode;
            item.productDefinition = i.productDefinition;
            item.productType = i.productType === 'service' ? t('Service product') : t('Stockable product');
            item.productBrand = (product.brand || {}).name || '';
            item.productDescription = (product.description || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
            item.productSalesNote = (product.salesNote || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
            item.description = (i.description || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
            item.barcode = i.barcode || product.barcode;
            item.hsCode = product.hsCode || '';
            item.countryOfOrigin = !!product.countryOfOriginId ? (countryOfOrigin || {}).name || '' : '';
            item.scheduledDate = i.scheduledDate;
            item.warehouse = (
                (await app.collection('inventory.warehouses').findOne({
                    _id: i.warehouseId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;
            item.quantity = f(i.quantity, 'unit');
            item.baseQuantity = f(i.baseQuantity, 'unit');
            item.unit = (unit || {}).name;
            item.unitSymbol = (unit || {}).symbol;
            item.baseQuantity = f(i.baseQuantity, 'unit');
            item.baseUnit = (baseUnit || {}).name;
            item.baseUnitSymbol = (baseUnit || {}).symbol;
            item.unitPrice = f(i.unitPrice, 'unit-price');
            item.unitPriceFC = f(i.unitPriceFC, 'unit-price');
            item.realUnitPrice = f((i.realTotal || 0) / (i.quantity || 1), 'unit-price');
            item.surplusGoodDescription = i.surplusGoodDescription || '';
            item.discount = f(i.discount, 'percentage');
            item.unitPriceAfterDiscount = f(i.unitPriceAfterDiscount, 'unit-price');
            item.tax = tax.label || tax.name;
            item.taxPercentage = f(tax.amount, 'percentage');
            item.taxTotal = f(i.taxTotal, 'total');
            item.grossUnitPriceAfterDiscount = f(i.grossUnitPriceAfterDiscount, 'total');
            item.realTotal = f(i.realTotal, 'total');
            item.total = f(i.total, 'total');
            item.grossTotal = f(i.grossTotal, 'total');

            if (!!i.currencyId) {
                const currency = await app.collection('kernel.currencies').findOne({
                    _id: i.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (typeof currency === 'object' && currency !== null) {
                    const formatOptions = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        },
                        useOutputPrecision: true
                    };
                    item.unitPriceFC = app.format(i.unitPriceFC, 'unit-price', formatOptions);
                    item.currency = currency.name;
                }
            }
            item.currencyRate = i.currencyRate;

            const um = product.unitMeasurements.find(um => um.unitId === i.baseUnitId);
            if (!!um && !!lengthUnit && !!weightUnit) {
                item.height = 0;
                item.heightUnitId = lengthUnit._id;
                item.width = 0;
                item.widthUnitId = lengthUnit._id;
                item.depth = 0;
                item.depthUnitId = lengthUnit._id;
                item.netWeight = 0;
                item.netWeightUnitId = weightUnit._id;
                item.grossWeight = 0;
                item.grossWeightUnitId = weightUnit._id;
                if (!!um) {
                    if (um.height && um.heightUnitId) {
                        item.height = um.height;
                        item.heightUnitId = um.heightUnitId;
                    }
                    if (um.width && um.widthUnitId) {
                        item.width = um.width;
                        item.widthUnitId = um.widthUnitId;
                    }
                    if (um.depth && um.depthUnitId) {
                        item.depth = um.depth;
                        item.depthUnitId = um.depthUnitId;
                    }
                    if (um.netWeight && um.netWeightUnitId) {
                        item.netWeight = um.netWeight;
                        item.netWeightUnitId = um.netWeightUnitId;
                    }
                    if (um.grossWeight && um.grossWeightUnitId) {
                        item.grossWeight = um.grossWeight;
                        item.grossWeightUnitId = um.grossWeightUnitId;
                    }
                }
                const heightUnit = unitsMap[item.heightUnitId];
                if (heightUnit) {
                    if (heightUnit.type === 'smaller') item.height = item.height / heightUnit.ratio;
                    else if (heightUnit.type === 'bigger') item.height = item.height * heightUnit.ratio;
                }
                const widthUnit = unitsMap[item.widthUnitId];
                if (widthUnit) {
                    if (widthUnit.type === 'smaller') item.width = item.width / widthUnit.ratio;
                    else if (widthUnit.type === 'bigger') item.width = item.width * widthUnit.ratio;
                }
                const depthUnit = unitsMap[item.depthUnitId];
                if (depthUnit) {
                    if (depthUnit.type === 'smaller') item.depth = item.depth / depthUnit.ratio;
                    else if (depthUnit.type === 'bigger') item.depth = item.depth * depthUnit.ratio;
                }
                const netWeightUnit = unitsMap[item.netWeightUnitId];
                if (netWeightUnit) {
                    if (netWeightUnit.type === 'smaller') item.netWeight = item.netWeight / netWeightUnit.ratio;
                    else if (netWeightUnit.type === 'bigger') item.netWeight = item.netWeight * netWeightUnit.ratio;
                }
                const grossWeightUnit = unitsMap[item.grossWeightUnitId];
                if (grossWeightUnit) {
                    if (grossWeightUnit.type === 'smaller') item.grossWeight = item.grossWeight / grossWeightUnit.ratio;
                    else if (grossWeightUnit.type === 'bigger')
                        item.grossWeight = item.grossWeight * grossWeightUnit.ratio;
                }
                item.totalWeight = round(round(item.grossWeight) * round(i.baseQuantity));
                totalQty += i.baseQuantity;
                totalWeight += round(round(item.grossWeight) * round(i.baseQuantity));
            }

            let stockStatusPercentage = 0;
            if (!(product.type === 'service' || !product.isSimple) && i.warehouseAvailableQuantity >= 0) {
                if (_.isNumber(i.warehouseAvailableQuantity) && _.isNumber(i.baseQuantity)) {
                    stockStatusPercentage = Math.max(
                        (i.warehouseAvailableQuantity - i.baseQuantity) / i.warehouseAvailableQuantity,
                        0
                    );
                } else {
                    stockStatusPercentage = 0;
                }
            }
            item.availableStock = i.warehouseAvailableQuantity;
            item.remainingStock = item.availableStock - i.baseQuantity;
            item.stockStatusPercentage = Math.min(Math.max(round(stockStatusPercentage * 100, 'percentage'), 0), 100);
            item.stockStatusColor = 'green';
            if (item.availableStock > 0 && item.remainingStock === 0) {
                item.stockStatusColor = 'green';
            } else if (item.availableStock > 0 && item.remainingStock < 0) {
                item.stockStatusColor = 'orange';
            } else if (item.availableStock <= 0) {
                item.stockStatusColor = 'red';
            }

            if (
                _.isPlainObject(i.additionalItemFields) &&
                !_.isEmpty(i.additionalItemFields) &&
                !!documentType &&
                Array.isArray(documentType.additionalItemFields)
            ) {
                const additionalFields = [];

                for (const fieldDefinition of documentType.additionalItemFields) {
                    const value = i.additionalItemFields[fieldDefinition.code] || '';
                    const field = {};

                    if (!fieldDefinition.visibleInPrintouts) {
                        continue;
                    }

                    field.code = fieldDefinition.code;
                    field.label = fieldDefinition.label;
                    field.subLabel = fieldDefinition.subLabel;
                    field.value = value;

                    if (field.type === 'integer') {
                        field.value = f(value, 'integer');
                    } else if (field.type === 'decimal') {
                        field.value = f(value, 'amount');
                    } else if (field.type === 'money') {
                        field.value = f(value, 'currency');
                    } else if (field.type === 'date') {
                        field.value = f(value, 'date');
                    } else if (field.type === 'datetime') {
                        field.value = f(value, 'datetime');
                    } else if (field.type === 'boolean') {
                        field.value = !!value ? t('Yes') : t('No');
                    }

                    additionalFields.push(field);
                }

                item.additionalFields = additionalFields;
            }

            items.push(item);
        }
        document.items = items;
        document.additionalItemFields = [];
        for (const item of items) {
            if (Array.isArray(item.additionalFields)) {
                for (const additionalField of item.additionalFields) {
                    if (!document.additionalItemFields.some(aif => aif.code === additionalField.code)) {
                        document.additionalItemFields.push({
                            code: additionalField.code,
                            label: additionalField.label,
                            subLabel: additionalField.subLabel
                        });
                    }
                }
            }
        }
        for (const additionalItemField of document.additionalItemFields) {
            document.items = document.items.map(item => {
                if (!Array.isArray(item.additionalFields)) item.additionalFields = [];

                if (item.additionalFields.findIndex(af => af.code === additionalItemField.code) === -1) {
                    item.additionalFields.push({
                        ...additionalItemField,
                        value: ' '
                    });
                }

                return item;
            });
        }
        document.totalQty = totalQty;
        document.totalWeight = totalWeight;

        // Grouped items.
        const groupedItems = [];
        if (d.items.some(item => !!item.itemGroup)) {
            const grouped = _.groupBy(
                d.items.filter(item => !!item.itemGroup),
                'itemGroup'
            );

            for (const name of Object.keys(grouped)) {
                if (name.includes('*')) {
                    const unStarred = name.replaceAll('*', '').trim();
                    const existingGroupIndex = groupedItems.findIndex(gi => gi.name === unStarred);

                    if (existingGroupIndex !== -1) {
                        const group = {};
                        group.name = null;
                        group.items = grouped[name].map(i => document.items.find(di => di.id === i.id));
                        group.subTotal = f(_.sumBy(grouped[name], 'total'), 'total');

                        groupedItems.splice(existingGroupIndex, 0, group);
                    } else {
                        const group = {};
                        group.name = name;
                        group.items = grouped[name].map(i => document.items.find(di => di.id === i.id));
                        group.subTotal = f(_.sumBy(grouped[name], 'total'), 'total');

                        groupedItems.push(group);
                    }
                } else {
                    const group = {};
                    group.name = name;
                    group.items = grouped[name].map(i => document.items.find(di => di.id === i.id));
                    group.subTotal = f(_.sumBy(grouped[name], 'total'), 'total');

                    groupedItems.push(group);
                }
            }
        }
        document.groupedItems = groupedItems;
        document.unGroupedItems = d.items.filter(item => !item.itemGroup);
        if (document.groupedItems.length < 1) delete document.groupedItems;
        if (document.unGroupedItems.length < 1) delete document.unGroupedItems;

        // Payment plan.
        document.paymentPlan = [];
        if (!!d.paymentPlan && Array.isArray(d.paymentPlan.items) && d.paymentPlan.items.length > 0) {
            const paymentTypes = [
                {value: 'cash', label: 'Cash'},
                {value: 'moneyTransfer', label: 'Money Transfer'},
                {value: 'cheque', label: 'Cheque'},
                {value: 'promissoryNote', label: 'Promissory note'},
                {value: 'creditCard', label: 'Credit card'},
                {value: 'pos', label: 'POS'}
            ];

            for (const item of d.paymentPlan.items) {
                if (item.documentType === 'pos') {
                    document.paymentPlan.push({
                        no: item.no,
                        type: `${t(paymentTypes.find(t => t.value === item.documentType).label)} (${t(
                            '{{count}} instalment(s)',
                            {count: item.installmentCount || 1}
                        )})`,
                        dueDate: f(item.dueDate, 'date'),
                        amount: f(item.total, 'currency')
                    });
                } else {
                    document.paymentPlan.push({
                        no: item.no,
                        type: t(paymentTypes.find(t => t.value === item.documentType).label),
                        dueDate: f(item.dueDate, 'date'),
                        amount: f(item.total, 'currency')
                    });
                }
            }
        }

        // Document images.
        document.images = [];
        if (Array.isArray(d.attachments) && d.attachments.length > 0) {
            const attachments = await app.collection('kernel.files').find({
                _id: {$in: d.attachments},
                extension: {$in: ['jpeg', 'jpg', 'png', 'webp']}
            });

            if (attachments.length > 0) {
                for (const attachment of attachments) {
                    document.images.push(app.absoluteUrl(`files/${attachment._id}`));
                }
            }
        }

        // Financial project.
        if (!!d.financialProjectId) {
            const financialProject = await app.collection('kernel.financial-projects').findOne({
                _id: d.financialProjectId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            document.financialProjectName = financialProject.name;
            document.financialProjectCode = financialProject.code;
        }

        // Additional information
        if (!_.isEmpty(d.additionalInformationId)) {
            const additionalInformation = await app.collection('kernel.additional-information').findOne({
                _id: d.additionalInformationId,
                $select: ['fields'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (additionalInformation) {
                const fields = [];
                for (const key of Object.keys(d.additionalInformation || {})) {
                    const field = (additionalInformation.fields || []).find(f => f.code === key);
                    let value = d.additionalInformation[key];

                    if (typeof value === 'undefined' || value === null) {
                        value = '';
                    } else {
                        if (field.fieldType === 'money') {
                            value = f(value, 'currency');
                        } else if (field.fieldType === 'decimal') {
                            value = f(value, 'amount');
                        } else if (field.fieldType === 'boolean') {
                            value = !!value ? t('Yes') : t('No');
                        } else if (field.fieldType === 'yes-no') {
                            value = value === 'yes' ? t('Yes') : t('No');
                        } else if (field.fieldType === 'date') {
                            value = f(value, 'date');
                        } else if (field.fieldType === 'datetime') {
                            value = f(value, 'datetime');
                        } else if (field.fieldType === 'time') {
                            value = f(value, 'time');
                        }
                    }

                    if (!value) {
                        continue;
                    }

                    fields.push({
                        name: key,
                        label: field.label,
                        value,
                        group: field.group || undefined
                    });
                }
                document.fields = fields;

                const groupedFields = [];
                if (fields.every(f => !!f.group)) {
                    const grouped = _.groupBy(fields, 'group');

                    for (const group of Object.keys(grouped)) {
                        groupedFields.push({
                            group,
                            fields: grouped[group]
                        });
                    }
                }
                if (groupedFields.length > 0) {
                    document.groupedFields = groupedFields;
                }
            }
        }

        return document;
    },
    async sample(app, params) {
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const f = (amount, type) => app.format(amount, type);
        const company = await app.collection('kernel.company').findOne({});
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'payment-planned', label: 'Payment Planned'},
            {value: 'approved', label: 'Approved'},
            {value: 'to-invoice', label: 'To Invoice'},
            {value: 'invoiced', label: 'Invoiced'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const document = {};

        // General.
        document.locale = locale;
        document.status = t(statusOptions.find(o => o.value === 'draft').label);
        document.code = 'SO00000001';
        document.partnerType = 'customer';
        document.reference = 'Test ref';
        document.documentType = (await app.collection('sale.document-types').findOne({})).name;
        document.partnerGroup = (await app.collection('kernel.partner-groups').findOne({})).name;
        document.contactPerson = 'Test contact';
        document.currency = company.currency.name;
        document.currencyRate = 1;
        document.recordDate = f(app.datetime.local().toJSDate(), 'datetime');
        document.orderDate = f(app.datetime.local().toJSDate(), 'date');
        document.scheduledDate = f(app.datetime.local().toJSDate(), 'date');
        document.content = 'Sample content line 1\nSample content line 2';
        document.partnerOrderReference = "PO00000001"
        document.documentNo = "DN0000001"
        document.note = `
Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam nihil placeat praesentium
sed? Accusantium aliquid architecto doloribus fuga illum maxime molestias mollitia nisi,
officiis quidem ratione, recusandae, repudiandae tenetur voluptatum? Lorem ipsum dolor sit
amet, consectetur adipisicing elit. Autem dignissimos doloribus nam omnis similique? Ab,
aperiam aut dicta facilis hic id ipsa, molestiae neque officiis omnis quam quibusdam
repudiandae suscipit!
        `
            .trim()
            .split('\r\n')
            .join('<br/>')
            .split('\n')
            .join('<br/>');

        // Totals.
        document.subTotal = f(1120, 'total');
        document.discount = f(10, 'percentage');
        document.discountAmount = f(112.0, 'total');
        document.subTotalAfterDiscount = f(1008.0, 'total');
        document.taxTotal = f(176.94, 'total');
        document.rounding = f(0.15, 'total');
        document.grandTotal = f(1185.09, 'total');
        document.appliedTaxes = [
            {
                label: 'KDV %18',
                percentage: f(18, 'percentage'),
                amount: f(173.34, 'total')
            },
            {
                label: 'KDV %8',
                percentage: f(8, 'percentage'),
                amount: f(3.6, 'total')
            }
        ];

        const fields = [
            {name: 'test1', label: 'Test 1', value: 'test 1', group: 'Group 1'},
            {name: 'test2', label: 'Test 2', value: 'test 2', group: 'Group 1'},
            {name: 'test3', label: 'Test 3', value: 'test 3', group: 'Group 1'},
            {name: 'test4', label: 'Test 4', value: 'test 4', group: 'Group 2'},
            {name: 'test5', label: 'Test 5', value: 'test 5', group: 'Group 2'},
            {name: 'test6', label: 'Test 6', value: 'test 6', group: 'Group 2'}
        ];
        document.fields = fields;

        const groupedFields = [];
        if (fields.every(f => !!f.group)) {
            const grouped = _.groupBy(fields, 'group');

            for (const group of Object.keys(grouped)) {
                groupedFields.push({
                    group,
                    fields: grouped[group]
                });
            }
        }
        document.groupedFields = groupedFields;

        const contact = {};
        contact.code = 'M00000001';
        contact.isCompany = true;
        contact.name = 'EnterSoft';
        contact.legalName = 'EnterSoft Inc';
        contact.email = '<EMAIL>';
        contact.language = 'Turkish';
        contact.currency = company.currency.name;
        contact.website = 'https://entersoft.com.tr';
        contact.identity = '11111111111';
        contact.tin = '3334445556';
        contact.taxDepartment = 'KARTAL';
        contact.phone = '+90(850) 454 45 45';
        contact.address = 'Cevizli Mahallesi, Tugay Yolu Caddesi, No: 20/A/10, 34846 Maltepe/İstanbul Türkiye';
        contact.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        contact.city = 'İstanbul';
        contact.district = 'Maltepe';
        contact.subDistrict = 'Cevizli Mahallesi';
        contact.street = 'Tugay Yolu Caddesi';
        contact.doorNumber = '20/A';
        contact.apartmentNumber = '10';
        contact.postalCode = '34846';
        document.contact = contact;

        // Partner.
        const partner = {};
        partner.code = 'M00000001';
        partner.isCompany = true;
        partner.name = 'EnterSoft';
        partner.legalName = 'EnterSoft Inc';
        partner.email = '<EMAIL>';
        partner.language = 'Turkish';
        partner.currency = company.currency.name;
        partner.website = 'https://entersoft.com.tr';
        partner.identity = '';
        partner.tin = '3334445556';
        partner.taxDepartment = 'KARTAL';
        partner.phone = '+90(850) 454 45 45';
        partner.address = 'Cevizli Mahallesi, Tugay Yolu Caddesi, No: 20/A/10, 34846 Maltepe/İstanbul Türkiye';
        partner.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        partner.city = 'İstanbul';
        partner.district = 'Maltepe';
        partner.subDistrict = 'Cevizli Mahallesi';
        partner.street = 'Tugay Yolu Caddesi';
        partner.doorNumber = '20/A';
        partner.apartmentNumber = '10';
        partner.postalCode = '34846';
        document.partner = partner;
        document.paymentTerm = 'Test payment term';

        // Delivery address.
        const deliveryAddress = {};
        deliveryAddress.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        deliveryAddress.city = 'İstanbul';
        deliveryAddress.district = 'Maltepe';
        deliveryAddress.subDistrict = 'Cevizli Mahallesi';
        deliveryAddress.street = 'Tugay Yolu Caddesi';
        deliveryAddress.doorNumber = '20/A';
        deliveryAddress.apartmentNumber = '10';
        deliveryAddress.postalCode = '34846';
        document.deliveryAddress = deliveryAddress;

        // Invoice address.
        document.invoiceAddress = deliveryAddress;

        // Partner info.
        document.partnerName = document.partner.name;
        document.partnerAddress = deliveryAddress.address;

        // Organization.
        document.organization = 'Default sales organization';
        document.salesManager = 'Tarık AKTAŞ';
        document.salesManagerPosition = 'Sales manager';
        document.salesManagerOccupation = 'Test Occupation';
        document.salesManagerEmail = '<EMAIL>';
        document.salesManagerPhone = '+90 505 787 84 12';
        document.salesperson = 'Mahmut AKTAŞ';
        document.salespersonPhone = '+90 505 787 84 12';
        document.salespersonPosition = 'Salesperson';
        document.salespersonOccupation = 'Test Occupation';
        document.salespersonEmail = '<EMAIL>';

        document.additionalInformation = [
            {name: 'Test name', value: 'Test value'},
            {name: 'Test name 2', value: 'Test value 2'}
        ];

        // Logistics.
        document.warehouse = 'Central warehouse';
        document.deliveryPriority = 'Normal';
        document.deliveryPolicy = 'When all products are ready';
        document.deliveryCondition = 'Sample';
        document.deliveryMethod = 'Sample';
        document.carrier = 'DHL';
        document.cargoTrackingCode = 'AAAD333334';
        document.shippingPaymentType = ' Freight prepaid';

        // Items.
        document.items = [
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000001',
                productDefinition: 'Test product 1',
                productType: t('Stockable product'),
                productBrand: 'EnterERP',
                productDescription: 'Sample product description\nwith new line'
                    .split('\r\n')
                    .join('<br/>')
                    .split('\n')
                    .join('<br/>'),
                productSalesNote: 'Sample sales note',
                barcode: '1200124526541',
                hsCode: '1200124526FFG541',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 1',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(3, 'unit'),
                currency: 'TL',
                currencyRate: 1,
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(3, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(100, 'unit-price'),
                unitPriceFC: f(100, 'unit-price'),
                realUnitPrice: f(100, 'unit-price'),
                surplusGoodDescription: '3 + 1',
                discount: f(10, 'percentage'),
                unitPriceAfterDiscount: f(90, 'unit-price'),
                tax: 'KDV 18',
                taxPercentage: 18,
                taxTotal: f(16.2, 'total'),
                grossUnitPriceAfterDiscount: f(106.2, 'unit-price'),
                realTotal: f(90 * 3, 'total'),
                total: f(90 * 3, 'total'),
                grossTotal: f(106.2 * 3, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            },
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000002',
                productDefinition: 'Test product 2',
                productType: t('Stockable product'),
                productBrand: 'EnterERP',
                productDescription: 'Sample product description\nwith new line',
                productSalesNote: 'Sample sales note',
                barcode: '1200124526542',
                hsCode: '120012452ADF6541',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 2',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(1, 'unit'),
                currency: 'USD',
                currencyRate: 1,
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(1, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(50, 'unit-price'),
                unitPriceFC: f(50, 'unit-price'),
                realUnitPrice: f(50, 'unit-price'),
                surplusGoodDescription: '',
                discount: f(0, 'percentage'),
                unitPriceAfterDiscount: f(50, 'unit-price'),
                tax: 'KDV 8',
                taxPercentage: 8,
                taxTotal: f(4, 'total'),
                grossUnitPriceAfterDiscount: f(54, 'unit-price'),
                realTotal: f(50, 'total'),
                total: f(50, 'total'),
                grossTotal: f(54, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            },
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000003',
                productDefinition: 'Test product 3',
                productBrand: 'EnterERP',
                productType: t('Stockable product'),
                productDescription: 'Sample product description\nwith new line',
                productSalesNote: 'Sample sales note',
                barcode: '1200124526543',
                hsCode: '120012452AA6541',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 3',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(5, 'unit'),
                currency: 'EUR',
                currencyRate: 1,
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(5, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(200, 'unit-price'),
                unitPriceFC: f(200, 'unit-price'),
                realUnitPrice: f(200, 'unit-price'),
                discount: f(20, 'percentage'),
                surplusGoodDescription: '6 + 2',
                unitPriceAfterDiscount: f(160, 'unit-price'),
                tax: 'KDV 18',
                taxPercentage: 18,
                taxTotal: f(28.8, 'total'),
                grossUnitPriceAfterDiscount: f(228.8, 'unit-price'),
                realTotal: f(160 * 5, 'total'),
                total: f(160 * 5, 'total'),
                grossTotal: f(228.8 * 5, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            }
        ];
        document.additionalItemFields = [
            {code: 'test1', label: 'Test 1', subLabel: 'Demo 1'},
            {code: 'test2', label: 'Test 2', subLabel: 'Demo 2'}
        ];
        document.groupedItems = [
            {
                name: 'Test group 1',
                subTotal: f(1850, 'total'),
                items: document.items.slice(1)
            },
            {
                name: 'Test group 2',
                subTotal: f(1850, 'total'),
                items: document.items
            },
            {
                name: 'Test group 3',
                subTotal: f(1850, 'total'),
                items: document.items.slice(2)
            }
        ];
        document.unGroupedItems = document.items;
        document.totalQty = 0;
        document.totalWeight = 0;
        document.exchangeRates = [
            {rate: 30.0, currencyName: 'USD'},
            {rate: 32.2, currencyName: 'EUR'},
            {rate: 37.5, currencyName: 'GBP'},
            {rate: 0.32, currencyName: 'RUB'}
        ];

        document.paymentPlan = [
            {no: 1, type: t('Cash'), dueDate: f(app.datetime.local().toJSDate(), 'date'), amount: f(250, 'total')},
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 30}).toJSDate(), 'date'),
                amount: f(250, 'total')
            },
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 60}).toJSDate(), 'date'),
                amount: f(250, 'total')
            },
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 90}).toJSDate(), 'date'),
                amount: f(250 + 185, 'total')
            }
        ];

        document.financialProjectName = 'Test project';
        document.financialProjectCode = 'TP';

        document.images = [
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png')
        ];

        return document;
    },
    content: (app, recordId) =>
        fs.readFile(path.join(app.config('paths.static'), 'templates/sale/order.hbs'), {encoding: 'utf8'})
};
